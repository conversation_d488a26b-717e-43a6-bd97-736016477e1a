<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>xinjian</artifactId>
    <groupId>com.xinjian</groupId>
    <version>1.0.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>xinjian-common</artifactId>

  <description>通用工具模块，包含各种工具类、枚举、注解等</description>

  <dependencies>

    <!-- Spring 框架 -->
    <!-- Spring 上下文支持，提供对第三方库（如 Caching, Email, Scheduling）的集成支持 -->
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context-support</artifactId>
    </dependency>
    <!-- Spring Boot Security，用于身份验证和授权 -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-security</artifactId>
    </dependency>
    <!-- Spring Boot Validation，提供数据校验支持 -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>

    <!-- Problem Spring Web 处理 RESTful API 异常 -->
    <dependency>
      <groupId>org.zalando</groupId>
      <artifactId>problem-spring-web-starter</artifactId>
    </dependency>

    <!-- 数据库和分页 -->

    <!-- MyBatis Plus Starter -->
    <dependency>
      <groupId>com.xinjian</groupId>
      <artifactId>xinjian-starter-mybatis</artifactId>
    </dependency>

    <!-- JSON 库 -->
    <!-- Jackson，Spring 默认的 JSON 处理库 -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.module</groupId>
      <artifactId>jackson-module-parameter-names</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jdk8</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jsr310</artifactId>
    </dependency>

    <!-- 工具库 -->
    <!-- Hutool Java 工具集 -->
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
    </dependency>
    <!-- Apache Commons Lang3，提供对 Java 核心类的扩展 -->
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
    </dependency>
    <!-- Apache Commons IO，提供 IO 相关操作的工具类 -->
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
    </dependency>
    <!-- Google Guava，Google 的核心 Java 库 -->
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <!-- ip2region，用于 IP 地理位置查询 -->
    <dependency>
      <groupId>org.lionsoul</groupId>
      <artifactId>ip2region</artifactId>
    </dependency>
    <!-- YAUAA，User-Agent 解析库 -->
    <dependency>
      <groupId>nl.basjes.parse.useragent</groupId>
      <artifactId>yauaa</artifactId>
    </dependency>
    <!-- Apache POI，用于操作 Microsoft Office 格式文件 -->
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
    </dependency>
    <!-- Lombok，通过注解简化 Java 代码 -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>
    <!-- MapStruct Bean 映射工具 -->
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct-processor</artifactId>
      <scope>provided</scope>
    </dependency>
    <!-- 安全和 API -->
    <!-- JJWT，用于处理 JSON Web Tokens (JWT) -->
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-api</artifactId>
    </dependency>
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-impl</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-jackson</artifactId>
      <scope>runtime</scope>
    </dependency>
    <!-- JAXB API，用于 XML 和 Java 对象之间的绑定 -->
    <dependency>
      <groupId>javax.xml.bind</groupId>
      <artifactId>jaxb-api</artifactId>
    </dependency>
    <!-- Bouncy Castle，一个开源的加密算法库 -->
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-lts8on</artifactId>
      <version>${bcprov-lts8on.version}</version>
    </dependency>

    <!-- Web 环境 -->
    <!-- Servlet API，Web 应用开发的基础 API -->
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
    </dependency>

    <!-- Test Dependencies -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <scope>test</scope>
    </dependency>

  </dependencies>

</project>
