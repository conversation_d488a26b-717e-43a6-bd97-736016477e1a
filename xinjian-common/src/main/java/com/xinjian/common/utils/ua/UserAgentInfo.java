package com.xinjian.common.utils.ua;

import lombok.Builder;
import lombok.Data;

/** User-Agent 解析结果信息 */
@Data
@Builder
public class UserAgentInfo {

  /** 设备类型 (Desktop, Mobile, Tablet, Phone, etc.) */
  private String deviceType;

  /** 操作系统名称 */
  private String operatingSystem;

  /** 浏览器名称 */
  private String browserName;

  /** 浏览器版本 */
  private String browserVersion;

  /** 操作系统版本 */
  private String osVersion;

  /** 设备制造商 */
  private String deviceBrand;

  /** 设备型号 */
  private String deviceModel;

  /** 是否为移动设备 */
  private Boolean mobile;

  /**
   * 获取完整的浏览器信息（名称 + 版本）
   *
   * @return 完整的浏览器信息
   */
  public String getFullBrowserInfo() {
    if (browserName == null) {
      return "未知";
    }
    if (browserVersion == null) {
      return browserName;
    }
    return browserName + " " + browserVersion;
  }

  /**
   * 获取完整的操作系统信息（名称 + 版本）
   *
   * @return 完整的操作系统信息
   */
  public String getFullOsInfo() {
    if (operatingSystem == null) {
      return "未知";
    }
    if (osVersion == null) {
      return operatingSystem;
    }
    return operatingSystem + " " + osVersion;
  }
}
