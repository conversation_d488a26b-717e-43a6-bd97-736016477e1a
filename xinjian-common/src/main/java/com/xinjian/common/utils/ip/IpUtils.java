package com.xinjian.common.utils.ip;

import com.google.common.net.InetAddresses;
import com.xinjian.common.utils.ServletUtils;
import java.net.InetAddress;
import java.net.UnknownHostException;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;

/**
 * IP 地址工具类
 *
 * <p>只负责 IP 地址的获取和基础校验，支持 IPv4/IPv6
 */
public final class IpUtils {

  // 常见的用于获取 IP 的 HTTP 请求头
  private static final String[] IP_HEADER_CANDIDATES = {
    "X-Forwarded-For",
    "Proxy-Client-IP",
    "WL-Proxy-Client-IP",
    "HTTP_X_FORWARDED_FOR",
    "HTTP_CLIENT_IP",
    "HTTP_X_CLIENT_IP",
    "X-Real-IP"
  };

  private IpUtils() {
    // 工具类，私有构造函数
  }

  /**
   * 获取客户端 IP
   *
   * @return IP 地址
   */
  public static String getIpAddr() {
    return getIpAddr(ServletUtils.getRequest());
  }

  /**
   * 从 HttpServletRequest 中获取客户端的真实 IP 地址
   *
   * @param request HttpServletRequest 对象
   * @return 客户端 IP 地址，获取失败则返回 "unknown"
   */
  public static String getIpAddr(HttpServletRequest request) {
    if (request == null) {
      return "unknown";
    }

    // 依次从不同的请求头中尝试获取 IP
    for (String header : IP_HEADER_CANDIDATES) {
      String ipList = request.getHeader(header);
      if (StringUtils.isNotBlank(ipList) && !"unknown".equalsIgnoreCase(ipList)) {
        // X-Forwarded-For 可能包含多个 IP，取第一个非 "unknown" 的有效 IP
        return getFirstValidIpFromList(ipList);
      }
    }

    // 如果所有请求头都没有，则获取远程地址
    String ip = request.getRemoteAddr();
    // 处理本地回环地址
    return "0:0:0:0:0:0:0:1".equals(ip) ? "127.0.0.1" : ip;
  }

  /**
   * 从逗号分隔的 IP 地址列表中获取第一个有效的 IP
   *
   * @param ipList IP 地址列表字符串
   * @return 第一个有效的 IP 地址
   */
  private static String getFirstValidIpFromList(String ipList) {
    if (ipList.indexOf(',') > 0) {
      String[] ips = ipList.split(",");
      for (String ip : ips) {
        String trimmedIp = ip.trim();
        if (isIp(trimmedIp)) {
          return trimmedIp;
        }
      }
    }
    return ipList.trim();
  }

  /**
   * 检查是否为内部 IP 地址
   *
   * @param ip IP 地址
   * @return 结果
   */
  public static boolean internalIp(String ip) {
    return isInternalIp(ip);
  }

  /**
   * 判断给定的 IP 地址是否为内部（私有）IP
   *
   * @param ip IP 地址字符串
   * @return 如果是内部 IP 则返回 true，否则返回 false
   */
  public static boolean isInternalIp(String ip) {
    if (StringUtils.isBlank(ip)) {
      return false;
    }
    try {
      InetAddress addr = InetAddresses.forString(ip);
      return addr.isSiteLocalAddress() || addr.isLoopbackAddress();
    } catch (IllegalArgumentException e) {
      return false; // 不是合法的 IP 地址格式
    }
  }

  /**
   * 判断给定字符串是否为有效的 IP 地址 (IPv4 或 IPv6)
   *
   * @param ip IP 字符串
   * @return 如果是有效 IP 则返回 true，否则返回 false
   */
  public static boolean isIp(String ip) {
    return InetAddresses.isInetAddress(ip);
  }

  /**
   * 获取 IP 地址
   *
   * @return 本地 IP 地址
   */
  public static String getHostIp() {
    try {
      return InetAddress.getLocalHost().getHostAddress();
    } catch (UnknownHostException e) {
      return "127.0.0.1";
    }
  }

  /**
   * 获取主机名
   *
   * @return 本地主机名
   */
  public static String getHostName() {
    try {
      return InetAddress.getLocalHost().getHostName();
    } catch (UnknownHostException e) {
      return "未知";
    }
  }

  /**
   * 从多级反向代理中获得第一个非 unknown IP 地址
   *
   * @param ip 获得的 IP 地址
   * @return 第一个非 unknown IP 地址
   */
  public static String getMultistageReverseProxyIp(String ip) {
    // 多级反向代理检测
    if (ip != null && ip.indexOf(",") > 0) {
      final String[] ips = ip.trim().split(",");
      for (String subIp : ips) {
        if (false == isUnknown(subIp)) {
          ip = subIp;
          break;
        }
      }
    }
    return StringUtils.substring(ip, 0, 255);
  }

  /**
   * 检测给定字符串是否为未知，多用于检测 HTTP 请求相关
   *
   * @param checkString 被检测的字符串
   * @return 是否未知
   */
  public static boolean isUnknown(String checkString) {
    return StringUtils.isBlank(checkString) || "unknown".equalsIgnoreCase(checkString);
  }
}
