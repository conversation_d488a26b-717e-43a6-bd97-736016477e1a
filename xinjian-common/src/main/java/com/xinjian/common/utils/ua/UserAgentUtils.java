package com.xinjian.common.utils.ua;

import javax.servlet.http.HttpServletRequest;
import nl.basjes.parse.useragent.UserAgent;
import nl.basjes.parse.useragent.UserAgentAnalyzer;

/**
 * User-Agent 解析工具类 (基于 YAUAA)
 *
 * <p>特点：
 *
 * <ul>
 *   <li>支持各种设备和浏览器的详细解析
 *   <li>线程安全，性能优秀
 * </ul>
 */
public final class UserAgentUtils {

  private static final UserAgentAnalyzer ANALYZER;

  // YAUAA 的 UserAgentAnalyzer 初始化成本较高，且线程安全，
  // 因此通过静态代码块全局初始化一次。
  static {
    ANALYZER =
        UserAgentAnalyzer.newBuilder()
            .withField(UserAgent.DEVICE_CLASS)
            .withField(UserAgent.OPERATING_SYSTEM_NAME)
            .withField(UserAgent.OPERATING_SYSTEM_VERSION)
            .withField(UserAgent.AGENT_NAME)
            .withField(UserAgent.AGENT_VERSION)
            .withField(UserAgent.DEVICE_BRAND)
            .withField(UserAgent.DEVICE_NAME)
            .withField("DeviceClass")
            .hideMatcherLoadStats()
            .withCache(10000) // 设置内部缓存大小
            .build();
  }

  private UserAgentUtils() {
    // 工具类，私有构造函数
  }

  /**
   * 从 HttpServletRequest 中解析 User-Agent 信息。
   *
   * @param request HttpServletRequest 对象
   * @return UserAgentInfo 对象，如果 UA 头不存在则返回 null
   */
  public static UserAgentInfo parse(HttpServletRequest request) {
    if (request == null) {
      return null;
    }
    String uaString = request.getHeader("User-Agent");
    return parse(uaString);
  }

  /**
   * 解析 User-Agent 字符串。
   *
   * @param userAgentString User-Agent 字符串
   * @return UserAgentInfo 对象，如果字符串为空则返回 null
   */
  public static UserAgentInfo parse(String userAgentString) {
    if (userAgentString == null || userAgentString.isEmpty()) {
      return null;
    }

    try {
      UserAgent agent = ANALYZER.parse(userAgentString);

      return UserAgentInfo.builder()
          .deviceType(agent.getValue(UserAgent.DEVICE_CLASS))
          .operatingSystem(agent.getValue(UserAgent.OPERATING_SYSTEM_NAME))
          .osVersion(agent.getValue(UserAgent.OPERATING_SYSTEM_VERSION))
          .browserName(agent.getValue(UserAgent.AGENT_NAME))
          .browserVersion(agent.getValue(UserAgent.AGENT_VERSION))
          .deviceBrand(agent.getValue(UserAgent.DEVICE_BRAND))
          .deviceModel(agent.getValue(UserAgent.DEVICE_NAME))
          .mobile("Mobile".equals(agent.getValue(UserAgent.DEVICE_CLASS)))
          .build();
    } catch (Exception e) {
      // 解析失败时返回基本信息
      return UserAgentInfo.builder()
          .deviceType("未知")
          .operatingSystem("未知")
          .browserName("未知")
          .build();
    }
  }

  /**
   * 判断是否为移动设备
   *
   * @param request HttpServletRequest 对象
   * @return 如果是移动设备则返回 true，否则返回 false
   */
  public static boolean isMobile(HttpServletRequest request) {
    UserAgentInfo info = parse(request);
    return info != null && Boolean.TRUE.equals(info.getMobile());
  }

  /**
   * 判断是否为移动设备
   *
   * @param userAgentString User-Agent 字符串
   * @return 如果是移动设备则返回 true，否则返回 false
   */
  public static boolean isMobile(String userAgentString) {
    UserAgentInfo info = parse(userAgentString);
    return info != null && Boolean.TRUE.equals(info.getMobile());
  }

  /**
   * 获取设备类型（桌面端、移动端、平板等）
   *
   * @param request HttpServletRequest 对象
   * @return 设备类型，解析失败则返回"未知"
   */
  public static String getDeviceType(HttpServletRequest request) {
    UserAgentInfo info = parse(request);
    return info != null ? info.getDeviceType() : "未知";
  }

  /**
   * 获取设备类型（桌面端、移动端、平板等）
   *
   * @param userAgentString User-Agent 字符串
   * @return 设备类型，解析失败则返回"未知"
   */
  public static String getDeviceType(String userAgentString) {
    UserAgentInfo info = parse(userAgentString);
    return info != null ? info.getDeviceType() : "未知";
  }

  /**
   * 获取浏览器名称
   *
   * @param request HttpServletRequest 对象
   * @return 浏览器名称，解析失败则返回"未知"
   */
  public static String getBrowserName(HttpServletRequest request) {
    UserAgentInfo info = parse(request);
    return info != null ? info.getBrowserName() : "未知";
  }

  /**
   * 获取浏览器名称
   *
   * @param userAgentString User-Agent 字符串
   * @return 浏览器名称，解析失败则返回"未知"
   */
  public static String getBrowserName(String userAgentString) {
    UserAgentInfo info = parse(userAgentString);
    return info != null ? info.getBrowserName() : "未知";
  }

  /**
   * 获取操作系统名称
   *
   * @param request HttpServletRequest 对象
   * @return 操作系统名称，解析失败则返回"未知"
   */
  public static String getOperatingSystem(HttpServletRequest request) {
    UserAgentInfo info = parse(request);
    return info != null ? info.getOperatingSystem() : "未知";
  }

  /**
   * 获取操作系统名称
   *
   * @param userAgentString User-Agent 字符串
   * @return 操作系统名称，解析失败则返回"未知"
   */
  public static String getOperatingSystem(String userAgentString) {
    UserAgentInfo info = parse(userAgentString);
    return info != null ? info.getOperatingSystem() : "未知";
  }
}
