package com.xinjian.common.core.domain.dto;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 用户会话信息
 *
 * <p>该类用于存储易变的会话信息，用于记录登录日志、在线用户列表等场景。这是重构 LoginUser 类后分离出的专门用于会话信息管理的类。
 */
@Data
public class UserSessionInfo {

  /** 令牌 */
  private String token;

  /** 用户 ID */
  private Long userId;

  /** 部门 ID */
  private Long deptId;

  /** 用户名 */
  private String username;

  /** 登录 IP 地址 */
  private String ipaddr;

  /** 登录地点 */
  private String loginLocation;

  /** 浏览器类型 */
  private String browser;

  /** 操作系统 */
  private String os;

  /** 登录时间 */
  private LocalDateTime loginTime;

  /** 过期时间 */
  private LocalDateTime expireTime;

  /** 默认构造函数 */
  public UserSessionInfo() {}

  /**
   * 带参数的构造函数
   *
   * @param token 令牌
   * @param userId 用户 ID
   * @param username 用户名
   * @param ipaddr 登录 IP 地址
   * @param loginLocation 登录地点
   * @param browser 浏览器类型
   * @param os 操作系统
   * @param loginTime 登录时间
   * @param expireTime 过期时间
   */
  public UserSessionInfo(
      String token,
      Long userId,
      String username,
      String ipaddr,
      String loginLocation,
      String browser,
      String os,
      LocalDateTime loginTime,
      LocalDateTime expireTime) {
    this.token = token;
    this.userId = userId;
    this.username = username;
    this.ipaddr = ipaddr;
    this.loginLocation = loginLocation;
    this.browser = browser;
    this.os = os;
    this.loginTime = loginTime;
    this.expireTime = expireTime;
  }
}
