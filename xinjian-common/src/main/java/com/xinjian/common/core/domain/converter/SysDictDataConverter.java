package com.xinjian.common.core.domain.converter;

import com.xinjian.common.core.domain.dto.SysDictDataDTO;
import com.xinjian.common.core.domain.entity.SysDictData;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SysDictDataConverter {

  SysDictDataDTO toDto(SysDictData entity);

  @Mapping(target = "dictCode", ignore = true)
  SysDictData toEntity(SysDictDataDTO dto);

  void updateEntity(SysDictDataDTO dto, @MappingTarget SysDictData entity);
}
