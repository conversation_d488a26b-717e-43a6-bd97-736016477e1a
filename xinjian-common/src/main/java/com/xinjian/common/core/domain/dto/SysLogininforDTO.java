package com.xinjian.common.core.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinjian.common.core.domain.BaseDTO;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysLogininforDTO extends BaseDTO {
  private static final long serialVersionUID = 1L;

  /** ID */
  private Long infoId;

  /** 用户账号 */
  private String userName;

  /** 登录状态 true 成功 false 失败 */
  private Boolean status;

  /** 登录 IP 地址 */
  private String ipaddr;

  /** 登录地点 */
  private String loginLocation;

  /** 浏览器类型 */
  private String browser;

  /** 操作系统 */
  private String os;

  /** 提示消息 */
  private String msg;

  /** 访问时间 */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime loginTime;
}
