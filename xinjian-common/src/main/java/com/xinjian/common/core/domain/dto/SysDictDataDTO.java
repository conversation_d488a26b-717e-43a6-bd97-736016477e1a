package com.xinjian.common.core.domain.dto;

import com.xinjian.common.core.domain.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysDictDataDTO extends BaseDTO {
  private static final long serialVersionUID = 1L;

  /** 字典编码 */
  private Long dictCode;

  /** 字典排序 */
  private Long dictSort;

  /** 字典标签 */
  private String dictLabel;

  /** 字典键值 */
  private String dictValue;

  /** 字典类型 */
  private String dictType;

  /** 样式属性（其他样式扩展） */
  private String cssClass;

  /** 表格字典样式 */
  private String listClass;

  /** 是否默认（Y 是 N 否） */
  private String isDefault;

  /** 状态（true 正常 false 停用） */
  private Boolean status;
}
