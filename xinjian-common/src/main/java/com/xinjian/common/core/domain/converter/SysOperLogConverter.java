package com.xinjian.common.core.domain.converter;

import com.xinjian.common.core.domain.dto.SysOperLogDTO;
import com.xinjian.common.core.domain.entity.SysOperLog;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SysOperLogConverter {

  SysOperLogDTO toDto(SysOperLog entity);

  @Mapping(target = "operId", ignore = true)
  SysOperLog toEntity(SysOperLogDTO dto);

  void updateEntity(SysOperLogDTO dto, @MappingTarget SysOperLog entity);
}
