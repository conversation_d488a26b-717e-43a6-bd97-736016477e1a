package com.xinjian.common.core.domain.dto;

import com.xinjian.common.core.domain.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysDictTypeDTO extends BaseDTO {
  private static final long serialVersionUID = 1L;

  /** 字典主键 */
  private Long dictId;

  /** 字典名称 */
  private String dictName;

  /** 字典类型 */
  private String dictType;

  /** 状态（true 正常 false 停用） */
  private Boolean status;
}
