package com.xinjian.common.core.domain.converter;

import com.xinjian.common.core.domain.dto.SysUserOnlineDTO;
import com.xinjian.common.core.domain.entity.SysUserOnline;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SysUserOnlineConverter {

  SysUserOnlineDTO toDto(SysUserOnline entity);

  @Mapping(target = "tokenId", ignore = true)
  SysUserOnline toEntity(SysUserOnlineDTO dto);

  void updateEntity(SysUserOnlineDTO dto, @MappingTarget SysUserOnline entity);
}
