package com.xinjian.common.core.domain.converter;

import com.xinjian.common.core.domain.dto.SysLogininforDTO;
import com.xinjian.common.core.domain.entity.SysLogininfor;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SysLogininforConverter {

  SysLogininforDTO toDto(SysLogininfor entity);

  @Mapping(target = "infoId", ignore = true)
  SysLogininfor toEntity(SysLogininforDTO dto);

  void updateEntity(SysLogininforDTO dto, @MappingTarget SysLogininfor entity);
}
