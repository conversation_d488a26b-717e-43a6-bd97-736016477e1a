package com.xinjian.common.core.domain.converter;

import com.xinjian.common.core.domain.dto.SysPostDTO;
import com.xinjian.common.core.domain.entity.SysPost;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SysPostConverter {

  SysPostDTO toDto(SysPost entity);

  @Mapping(target = "postId", ignore = true)
  SysPost toEntity(SysPostDTO dto);

  void updateEntity(SysPostDTO dto, @MappingTarget SysPost entity);
}
