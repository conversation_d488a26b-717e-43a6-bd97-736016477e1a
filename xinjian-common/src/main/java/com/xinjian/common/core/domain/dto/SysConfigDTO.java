package com.xinjian.common.core.domain.dto;

import com.xinjian.common.core.domain.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysConfigDTO extends BaseDTO {
  private static final long serialVersionUID = 1L;

  /** 参数主键 */
  private Long configId;

  /** 参数名称 */
  private String configName;

  /** 参数键名 */
  private String configKey;

  /** 参数键值 */
  private String configValue;

  /** 系统内置（Y 是 N 否） */
  private String configType;
}
