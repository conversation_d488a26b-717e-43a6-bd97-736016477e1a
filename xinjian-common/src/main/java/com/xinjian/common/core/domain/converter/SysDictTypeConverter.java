package com.xinjian.common.core.domain.converter;

import com.xinjian.common.core.domain.dto.SysDictTypeDTO;
import com.xinjian.common.core.domain.entity.SysDictType;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SysDictTypeConverter {

  SysDictTypeDTO toDto(SysDictType entity);

  @Mapping(target = "dictId", ignore = true)
  SysDictType toEntity(SysDictTypeDTO dto);

  void updateEntity(SysDictTypeDTO dto, @MappingTarget SysDictType entity);
}
