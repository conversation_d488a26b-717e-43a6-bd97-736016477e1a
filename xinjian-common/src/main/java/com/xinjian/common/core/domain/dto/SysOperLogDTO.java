package com.xinjian.common.core.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinjian.common.core.domain.BaseDTO;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysOperLogDTO extends BaseDTO {
  private static final long serialVersionUID = 1L;

  /** 日志主键 */
  private Long operId;

  /** 操作模块 */
  private String title;

  /** 业务类型（0 其它 1 新增 2 修改 3 删除） */
  private Integer businessType;

  /** 业务类型数组 */
  private Integer[] businessTypes;

  /** 请求方法 */
  private String method;

  /** 请求方式 */
  private String requestMethod;

  /** 操作类别（0 其它 1 后台用户 2 手机端用户） */
  private Integer operatorType;

  /** 操作人员 */
  private String operName;

  /** 部门名称 */
  private String deptName;

  /** 请求 url */
  private String operUrl;

  /** 操作地址 */
  private String operIp;

  /** 操作地点 */
  private String operLocation;

  /** 请求参数 */
  private String operParam;

  /** 返回参数 */
  private String jsonResult;

  /** 操作状态（true 正常 false 异常） */
  private Boolean status;

  /** 错误消息 */
  private String errorMsg;

  /** 操作时间 */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime operTime;

  /** 消耗时间 */
  private Long costTime;
}
