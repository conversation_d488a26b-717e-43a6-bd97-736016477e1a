package com.xinjian.common.core.domain.converter;

import com.xinjian.common.core.domain.dto.SysDeptDTO;
import com.xinjian.common.core.domain.entity.SysDept;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SysDeptConverter {

  SysDeptDTO toDto(SysDept entity);

  @Mapping(target = "deptId", ignore = true)
  @Mapping(target = "isDeleted", ignore = true)
  @Mapping(target = "remark", ignore = true)
  @Mapping(target = "searchValue", ignore = true)
  @Mapping(target = "parentName", ignore = true)
  @Mapping(target = "children", ignore = true)
  SysDept toEntity(SysDeptDTO dto);

  @Mapping(target = "isDeleted", ignore = true)
  @Mapping(target = "remark", ignore = true)
  @Mapping(target = "searchValue", ignore = true)
  @Mapping(target = "parentName", ignore = true)
  @Mapping(target = "children", ignore = true)
  void updateEntity(SysDeptDTO dto, @MappingTarget SysDept entity);

  List<SysDeptDTO> toDtoList(List<SysDept> entities);
}
