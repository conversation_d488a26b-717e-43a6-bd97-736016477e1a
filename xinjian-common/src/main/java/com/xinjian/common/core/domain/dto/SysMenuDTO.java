package com.xinjian.common.core.domain.dto;

import com.xinjian.common.core.domain.BaseDTO;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysMenuDTO extends BaseDTO {
  private static final long serialVersionUID = 1L;

  /** 菜单 ID */
  private Long menuId;

  /** 菜单名称 */
  private String menuName;

  /** 父菜单名称 */
  private String parentName;

  /** 父菜单 ID */
  private Long parentId;

  /** 显示顺序 */
  private Integer orderNum;

  /** 路由地址 */
  private String path;

  /** 组件路径 */
  private String component;

  /** 路由参数 */
  private String query;

  /** 是否为外链（true 是外链 false 不是外链） */
  private Boolean isFrame;

  /** 是否缓存（true 缓存 false 不缓存） */
  private Boolean isCache;

  /** 类型（M 目录 C 菜单 F 按钮） */
  private String menuType;

  /** 显示状态（true 显示 false 隐藏） */
  private Boolean visible;

  /** 菜单状态（true 正常 false 停用） */
  private Boolean status;

  /** 权限字符串 */
  private String perms;

  /** 菜单图标 */
  private String icon;

  /** 子菜单 */
  private List<SysMenuDTO> children = new ArrayList<>();
}
