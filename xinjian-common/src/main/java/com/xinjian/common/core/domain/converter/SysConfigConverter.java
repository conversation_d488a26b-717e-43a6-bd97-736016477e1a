package com.xinjian.common.core.domain.converter;

import com.xinjian.common.core.domain.dto.SysConfigDTO;
import com.xinjian.common.core.domain.entity.SysConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SysConfigConverter {

  SysConfigDTO toDto(SysConfig entity);

  @Mapping(target = "configId", ignore = true)
  SysConfig toEntity(SysConfigDTO dto);

  void updateEntity(SysConfigDTO dto, @MappingTarget SysConfig entity);
}
