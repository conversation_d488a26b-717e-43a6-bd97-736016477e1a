package com.xinjian.common.core.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class SysUserOnlineDTO {
  private static final long serialVersionUID = 1L;

  /** 会话编号 */
  private String tokenId;

  /** 部门名称 */
  private String deptName;

  /** 用户名称 */
  private String userName;

  /** 登录 IP 地址 */
  private String ipaddr;

  /** 登录地址 */
  private String loginLocation;

  /** 浏览器类型 */
  private String browser;

  /** 操作系统 */
  private String os;

  /** 登录时间 */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime loginTime;
}
