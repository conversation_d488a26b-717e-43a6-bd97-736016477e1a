package com.xinjian.common.core.domain.dto;

import com.xinjian.common.core.domain.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysPostDTO extends BaseDTO {
  private static final long serialVersionUID = 1L;

  /** 岗位序号 */
  private Long postId;

  /** 岗位编码 */
  private String postCode;

  /** 岗位名称 */
  private String postName;

  /** 岗位排序 */
  private Integer postSort;

  /** 状态（true 正常 false 停用） */
  private Boolean status;

  /** 用户是否存在此岗位标识 默认不存在 */
  private boolean flag = false;
}
