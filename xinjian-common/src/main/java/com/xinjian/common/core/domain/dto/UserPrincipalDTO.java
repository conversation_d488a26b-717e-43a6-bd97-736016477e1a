package com.xinjian.common.core.domain.dto;

import com.xinjian.common.core.domain.BaseDTO;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户认证信息 DTO 对象
 *
 * <p>专门用于系统内部传递构建认证主体所需的核心信息，包含密码等敏感信息，仅用于认证流程。
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserPrincipalDTO extends BaseDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /** 用户 ID */
  private Long userId;

  /** 部门 ID */
  private Long deptId;

  /** 用户账号 */
  private String userName;

  /** 用户密码（加密后的） */
  private String password;

  /** 帐号状态（true 正常 false 停用） */
  private Boolean status;

  /** 是否删除 */
  private Boolean isDeleted;
}
