package com.xinjian.common.core.domain.dto;

import com.xinjian.common.core.domain.BaseDTO;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** 用户角色关联 DTO 对象 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysUserRoleDTO extends BaseDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /** 用户 ID */
  private Long userId;

  /** 角色 ID */
  private Long roleId;
}
