package com.xinjian.common.core.domain.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * 登录用户身份权限 (不可变对象)
 *
 * <p>实现了 UserDetails 接口，作为 Spring Security 在安全上下文中存储的核心对象。该对象在构造后状态不应被改变，以确保授权信息在请求处理过程中的一致性和安全性。
 */
@Getter
@ToString
// 仅基于 userId 来判断用户是否"相等"，避免因权限等集合内容导致的不一致
@EqualsAndHashCode(of = "userId")
public final class LoginUser implements UserDetails {

  /** 用户 ID */
  private final Long userId;

  /** 部门 ID */
  private final Long deptId;

  /** 用户名 */
  private final String username;

  /** 密码 (已加密) */
  private final String password;

  /** 账户是否启用 */
  private final boolean enabled;

  /** 权限集合 */
  private final Set<String> permissions;

  /** 角色集合 */
  private final Set<String> roles;

  /**
   * 全参数构造函数（支持 JSON 反序列化）
   *
   * @param userId 用户 ID
   * @param deptId 部门 ID
   * @param username 用户名
   * @param password 密码
   * @param enabled 账户是否启用
   * @param permissions 权限集合
   * @param roles 角色集合
   */
  @JsonCreator
  public LoginUser(
      @JsonProperty("userId") Long userId,
      @JsonProperty("deptId") Long deptId,
      @JsonProperty("username") String username,
      @JsonProperty("password") String password,
      @JsonProperty("enabled") boolean enabled,
      @JsonProperty("permissions") Set<String> permissions,
      @JsonProperty("roles") Set<String> roles) {
    this.userId = userId;
    this.deptId = deptId;
    this.username = username;
    this.password = password;
    this.enabled = enabled;
    // 确保集合不为 null，避免后续代码出现 NullPointerException
    this.permissions = permissions == null ? Collections.emptySet() : permissions;
    this.roles = roles == null ? Collections.emptySet() : roles;
  }

  /**
   * 将角色和权限合并为 Spring Security 可识别的 GrantedAuthority 集合。内部权限模型和 Spring Security 授权引擎的桥梁。
   *
   * @return GrantedAuthority 集合
   */
  @Override
  @JsonIgnore
  public Collection<? extends GrantedAuthority> getAuthorities() {
    Set<String> authorityStrings = new HashSet<>();

    // 1. 添加角色信息（加上 "ROLE_" 前缀，这是 Spring Security 的惯例）
    this.roles.forEach(role -> authorityStrings.add("ROLE_" + role));

    // 2. 添加权限信息
    authorityStrings.addAll(this.permissions);

    // 3. 将合并后的字符串集合转换为 GrantedAuthority 对象集合
    return authorityStrings.stream().map(SimpleGrantedAuthority::new).collect(Collectors.toSet());
  }

  // --- UserDetails 接口的其他方法实现 ---

  @Override
  public String getPassword() {
    return this.password;
  }

  @Override
  public String getUsername() {
    return this.username;
  }

  /**
   * 账户是否未过期。可扩展为基于数据库字段判断。
   *
   * @return true 表示未过期
   */
  @Override
  public boolean isAccountNonExpired() {
    return true;
  }

  /**
   * 账户是否未锁定。可扩展为基于数据库字段判断（如登录失败次数过多）。
   *
   * @return true 表示未锁定
   */
  @Override
  public boolean isAccountNonLocked() {
    return true;
  }

  /**
   * 凭证（密码）是否未过期。可扩展为基于密码最后修改时间判断。
   *
   * @return true 表示未过期
   */
  @Override
  public boolean isCredentialsNonExpired() {
    return true;
  }

  /**
   * 账户是否启用。
   *
   * @return true 表示启用
   */
  @Override
  public boolean isEnabled() {
    return this.enabled;
  }
}
