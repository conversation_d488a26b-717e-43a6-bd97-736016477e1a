package com.xinjian.common.core.domain.converter;

import com.xinjian.common.core.domain.dto.SysMenuDTO;
import com.xinjian.common.core.domain.entity.SysMenu;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SysMenuConverter {

  SysMenuDTO toDto(SysMenu entity);

  @Mapping(target = "menuId", ignore = true)
  SysMenu toEntity(SysMenuDTO dto);

  void updateEntity(SysMenuDTO dto, @MappingTarget SysMenu entity);
}
