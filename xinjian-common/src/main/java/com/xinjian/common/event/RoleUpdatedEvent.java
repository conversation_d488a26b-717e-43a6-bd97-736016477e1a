package com.xinjian.common.event;

import org.springframework.context.ApplicationEvent;

/** 角色更新事件 当一个角色的信息或其权限发生变更时发布。 */
public class RoleUpdatedEvent extends ApplicationEvent {

  private final Long roleId;

  /**
   * 创建一个新的 RoleUpdatedEvent.
   *
   * @param source 事件发布者 (通常是 this)
   * @param roleId 被更新的角色的 ID
   */
  public RoleUpdatedEvent(Object source, Long roleId) {
    super(source);
    this.roleId = roleId;
  }

  public Long getRoleId() {
    return roleId;
  }
}
