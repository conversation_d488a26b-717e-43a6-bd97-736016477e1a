package com.xinjian.common.service.location;

import lombok.Builder;
import lombok.Data;
import lombok.With;
import org.apache.commons.lang3.StringUtils;

/** 地理位置信息 (适配 ip2region) */
@Data
@Builder
@With
public class GeoLocation {

  /** 国家 */
  private String country;

  /** 省份/区域 */
  private String province;

  /** 城市 */
  private String city;

  /** ISP/运营商 */
  private String isp;

  /** 原始 IP 地址 */
  private String originalIp;

  /**
   * 从 ip2region 的原始字符串解析地理位置信息 格式：国家 | 区域 | 省份 | 城市|ISP
   *
   * @param regionString ip2region 返回的原始字符串
   * @return GeoLocation 对象
   */
  public static GeoLocation fromRegionString(String regionString) {
    if (StringUtils.isBlank(regionString)) {
      return GeoLocation.builder().city("未知").build();
    }

    String[] parts = regionString.split("\\|");
    if (parts.length < 5) {
      return GeoLocation.builder().city("未知").build();
    }

    // 清理 "0" 值，转换为 null
    String country = "0".equals(parts[0]) ? null : parts[0];
    String province = "0".equals(parts[2]) ? null : parts[2];
    String city = "0".equals(parts[3]) ? null : parts[3];
    String isp = "0".equals(parts[4]) ? null : parts[4];

    return GeoLocation.builder().country(country).province(province).city(city).isp(isp).build();
  }

  /**
   * 获取完整的位置信息（国家 + 省份 + 城市）
   *
   * @return 完整的位置信息
   */
  public String getFullLocation() {
    StringBuilder sb = new StringBuilder();

    if (StringUtils.isNotBlank(country)) {
      sb.append(country);
    }

    if (StringUtils.isNotBlank(province)) {
      if (sb.length() > 0) {
        sb.append(" ");
      }
      sb.append(province);
    }

    if (StringUtils.isNotBlank(city)) {
      if (sb.length() > 0) {
        sb.append(" ");
      }
      sb.append(city);
    }

    return sb.length() > 0 ? sb.toString() : "未知";
  }

  /**
   * 获取简化的位置信息（省份 + 城市）
   *
   * @return 简化的位置信息
   */
  public String getSimpleLocation() {
    StringBuilder sb = new StringBuilder();

    if (StringUtils.isNotBlank(province)) {
      sb.append(province);
    }

    if (StringUtils.isNotBlank(city)) {
      if (sb.length() > 0) {
        sb.append(" ");
      }
      sb.append(city);
    }

    return sb.length() > 0 ? sb.toString() : "未知";
  }

  /**
   * 判断是否为内网 IP 地址
   *
   * @return 如果是内网 IP 则返回 true，否则返回 false
   */
  public boolean isInternal() {
    return "内网 IP".equals(city) || "未知".equals(city);
  }

  /**
   * 判断是否包含有效的地理位置信息
   *
   * @return 如果包含有效位置信息则返回 true，否则返回 false
   */
  public boolean hasValidLocation() {
    return !isInternal()
        && (StringUtils.isNotBlank(country)
            || StringUtils.isNotBlank(province)
            || StringUtils.isNotBlank(city));
  }
}
