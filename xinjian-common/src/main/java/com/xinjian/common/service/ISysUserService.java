package com.xinjian.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.common.core.domain.dto.SysUserDTO;
import com.xinjian.common.core.domain.dto.UserPrincipalDTO;
import com.xinjian.common.core.domain.query.SysUserQuery;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * 系统用户服务接口
 *
 * <p>定义用户相关的核心业务操作，主要用于框架层的安全认证等场景
 */
public interface ISysUserService {

  /**
   * 通过用户名查询用户
   *
   * @param userName 用户名
   * @return 用户对象信息
   */
  SysUserDTO selectUserByUserName(String userName);

  /**
   * 通过用户 ID 查询用户
   *
   * @param userId 用户 ID
   * @return 用户对象信息
   */
  SysUserDTO selectUserById(Long userId);

  /**
   * 通过用户名查询用户认证信息
   *
   * @param userName 用户名
   * @return 用户认证信息
   */
  UserPrincipalDTO selectUserPrincipalByUserName(String userName);

  /**
   * 通过用户 ID 查询用户认证信息
   *
   * @param userId 用户 ID
   * @return 用户认证信息
   */
  UserPrincipalDTO selectUserPrincipalById(Long userId);

  /**
   * 校验用户名称是否唯一
   *
   * @param userName 用户名称
   * @return 结果
   */
  boolean checkUserNameUnique(String userName);

  /**
   * 校验用户名称是否唯一
   *
   * @param user 用户信息
   * @return 结果
   */
  boolean checkUserNameUnique(SysUserDTO user);

  /**
   * 校验手机号码是否唯一
   *
   * @param user 用户信息
   * @return 结果
   */
  boolean checkMobileUnique(SysUserDTO user);

  /**
   * 校验 email 是否唯一
   *
   * @param user 用户信息
   * @return 结果
   */
  boolean checkEmailUnique(SysUserDTO user);

  /**
   * 注册用户信息
   *
   * @param user 用户信息
   * @return 结果
   */
  boolean registerUser(SysUserDTO user);

  /**
   * 修改用户基本信息
   *
   * @param user 用户信息
   * @return 结果
   */
  int updateUserProfile(SysUserDTO user);

  /**
   * 修改用户头像
   *
   * @param userName 用户名
   * @param avatar 头像地址
   * @return 结果
   */
  boolean updateUserAvatar(String userName, String avatar);

  /**
   * 更新用户头像
   *
   * @param userId 用户 ID
   * @param file 头像文件
   * @return 新头像的文件路径
   */
  String updateUserAvatar(Long userId, MultipartFile file);

  /**
   * 重置用户密码
   *
   * @param userName 用户名
   * @param password 密码
   * @return 结果
   */
  int resetUserPwd(String userName, String password);

  /**
   * 查询用户所属角色组
   *
   * @param userName 用户名
   * @return 结果
   */
  String selectUserRoleGroup(String userName);

  /**
   * 查询用户所属岗位组
   *
   * @param userName 用户名
   * @return 结果
   */
  String selectUserPostGroup(String userName);

  /**
   * 校验用户是否允许操作
   *
   * @param user 用户信息
   */
  void checkUserAllowed(SysUserDTO user);

  /**
   * 校验用户是否有数据权限
   *
   * @param userId 用户 id
   * @param currentUserId 当前用户 id
   */
  void checkUserDataScope(Long userId, Long currentUserId);

  /**
   * 新增保存用户信息
   *
   * @param user 用户信息
   * @return 结果
   */
  int insertUser(SysUserDTO user);

  /**
   * 修改保存用户信息
   *
   * @param user 用户信息
   * @return 结果
   */
  int updateUser(SysUserDTO user);

  /**
   * 修改用户状态
   *
   * @param user 用户信息
   * @return 结果
   */
  int updateUserStatus(SysUserDTO user);

  /**
   * 重置用户密码
   *
   * @param user 用户信息
   * @return 结果
   */
  int resetPwd(SysUserDTO user);

  /**
   * 删除用户信息
   *
   * @param userId 用户 ID
   * @return 结果
   */
  int deleteUserById(Long userId);

  /**
   * 批量删除用户信息
   *
   * @param userIds 需要删除的用户 ID
   */
  void deleteUserByIds(Long[] userIds);

  /**
   * 用户授权角色
   *
   * @param userId 用户 ID
   * @param roleIds 角色组
   */
  void insertUserAuth(Long userId, Long[] roleIds);

  /**
   * 用户授权角色（带用户 DTO）
   *
   * @param user 用户信息
   * @param roleIds 角色组
   */
  void insertUserAuthWithRoleIds(SysUserDTO user, Long[] roleIds);

  /**
   * 导出用户数据
   *
   * @param response 响应对象
   * @param query 查询参数
   */
  void export(HttpServletResponse response, SysUserQuery query);

  /**
   * 导入用户数据
   *
   * @param file 文件
   * @param updateSupport 是否更新支持
   * @param operName 操作用户
   * @return 结果
   */
  String importData(
      org.springframework.web.multipart.MultipartFile file, boolean updateSupport, String operName)
      throws Exception;

  /**
   * 导入模板
   *
   * @param response 响应对象
   */
  void importTemplate(HttpServletResponse response);

  /**
   * 根据条件分页查询用户列表
   *
   * @param query 查询参数对象
   * @return 分页用户信息集合信息
   */
  IPage<SysUserDTO> selectUserListByPage(SysUserQuery query);

  /**
   * 根据条件查询用户列表
   *
   * @param query 查询参数对象
   * @return 用户信息集合信息
   */
  List<SysUserDTO> selectUserList(SysUserQuery query);

  /**
   * 导入用户数据
   *
   * @param userList 用户数据列表
   * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
   * @param operName 操作用户
   * @return 结果
   */
  String importUser(List<SysUserDTO> userList, Boolean isUpdateSupport, String operName);

  /**
   * 根据条件分页查询已分配用户角色列表
   *
   * @param query 查询参数对象
   * @return 分页用户信息集合信息
   */
  IPage<SysUserDTO> selectAllocatedListByPage(SysUserQuery query);

  /**
   * 根据条件分页查询未分配用户角色列表
   *
   * @param query 查询参数对象
   * @return 分页用户信息集合信息
   */
  IPage<SysUserDTO> selectUnallocatedListByPage(SysUserQuery query);

  /**
   * 根据角色 ID 查询拥有该角色的所有用户 ID
   *
   * @param roleId 角色 ID
   * @return 用户 ID 列表
   */
  List<Long> findUserIdsByRoleId(Long roleId);
}
