package com.xinjian.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.common.core.domain.entity.SysConfig;
import com.xinjian.common.core.domain.query.SysConfigQuery;
import java.util.List;

/**
 * 系统配置服务接口
 *
 * <p>定义系统配置相关的核心业务操作，主要用于框架层的配置读取需求
 */
public interface ISysConfigService {

  /**
   * 根据配置 ID 查询配置信息
   *
   * @param configId 配置 ID
   * @return 配置信息
   */
  SysConfig selectConfigById(Long configId);

  /**
   * 根据配置键名查询配置值
   *
   * @param configKey 配置键名
   * @return 配置值
   */
  String selectConfigByKey(String configKey);

  /**
   * 查询验证码是否启用
   *
   * @return 是否启用验证码
   */
  boolean selectCaptchaEnabled();

  /**
   * 根据条件查询配置列表
   *
   * @param query 查询参数
   * @return 配置列表
   */
  List<SysConfig> selectConfigList(SysConfigQuery query);

  /** 加载配置缓存 */
  void loadingConfigCache();

  /**
   * 根据条件分页查询配置列表
   *
   * @param query 查询参数对象
   * @return 分页配置信息集合信息
   */
  IPage<SysConfig> selectConfigListByPage(SysConfigQuery query);

  /**
   * 新增保存配置信息
   *
   * @param config 配置信息
   * @return 结果
   */
  int insertConfig(SysConfig config);

  /**
   * 校验配置键名是否唯一
   *
   * @param config 配置信息
   * @return 结果
   */
  boolean checkConfigKeyUnique(SysConfig config);

  /**
   * 修改保存配置信息
   *
   * @param config 配置信息
   * @return 结果
   */
  int updateConfig(SysConfig config);

  /**
   * 批量删除配置信息
   *
   * @param configIds 需要删除的配置 ID
   */
  void deleteConfigByIds(Long[] configIds);

  /** 重置配置缓存 */
  void resetConfigCache();
}
