package com.xinjian.common.service;

import com.xinjian.common.core.domain.dto.SysRoleDTO;
import com.xinjian.common.core.domain.dto.SysUserRoleDTO;
import com.xinjian.common.core.domain.query.SysRoleQuery;
import java.util.List;
import java.util.Set;

/**
 * 系统角色服务接口
 *
 * <p>定义角色相关的核心业务操作，主要用于权限管理场景
 */
public interface ISysRoleService {

  /**
   * 根据用户 ID 查询角色权限标识集合
   *
   * @param userId 用户 ID
   * @return 角色权限标识集合
   */
  Set<String> selectRolePermissionByUserId(Long userId);

  /**
   * 根据用户 ID 查询角色列表
   *
   * @param userId 用户 ID
   * @return 角色列表
   */
  List<String> selectRoleKeysByUserId(Long userId);

  /**
   * 校验角色是否允许操作
   *
   * @param role 角色信息
   */
  void checkRoleAllowed(SysRoleDTO role);

  /**
   * 校验角色是否有数据权限
   *
   * @param currentUserId 当前用户 ID
   * @param roleId 角色 ID
   */
  void checkRoleDataScope(Long currentUserId, Long roleId);

  /**
   * 修改角色状态
   *
   * @param role 角色信息
   * @return 结果
   */
  int updateRoleStatus(SysRoleDTO role);

  /**
   * 删除角色信息
   *
   * @param roleIds 需要删除的角色 ID
   */
  void deleteRoleByIds(Long[] roleIds);

  /**
   * 查询所有角色
   *
   * @return 角色列表
   */
  List<SysRoleDTO> selectRoleAll();

  /**
   * 根据条件分页查询角色列表
   *
   * @param query 查询参数对象
   * @return 分页角色信息集合信息
   */
  List<SysRoleDTO> selectRoleList(SysRoleQuery query);

  /**
   * 根据角色 ID 查询角色信息
   *
   * @param roleId 角色 ID
   * @return 角色信息
   */
  SysRoleDTO selectRoleById(Long roleId);

  /**
   * 校验角色名称是否唯一
   *
   * @param role 角色信息
   * @return 结果
   */
  boolean checkRoleNameUnique(SysRoleDTO role);

  /**
   * 校验角色权限是否唯一
   *
   * @param role 角色信息
   * @return 结果
   */
  boolean checkRoleKeyUnique(SysRoleDTO role);

  /**
   * 新增保存角色信息
   *
   * @param role 角色信息
   * @return 结果
   */
  int insertRole(SysRoleDTO role);

  /**
   * 修改保存角色信息
   *
   * @param role 角色信息
   * @return 结果
   */
  int updateRole(SysRoleDTO role);

  /**
   * 取消授权用户角色
   *
   * @param userRole 用户和角色关联信息
   * @return 结果
   */
  int deleteAuthUser(SysUserRoleDTO userRole);

  /**
   * 批量取消授权用户角色
   *
   * @param roleId 角色 ID
   * @param userIds 需要删除的用户 ID
   * @return 结果
   */
  int deleteAuthUsers(Long roleId, Long[] userIds);

  /**
   * 批量选择用户授权
   *
   * @param roleId 角色 ID
   * @param userIds 需要授权的用户 ID
   * @return 结果
   */
  int insertAuthUsers(Long roleId, Long[] userIds);

  /**
   * 修改角色数据权限
   *
   * @param role 角色信息
   * @return 结果
   */
  int authDataScope(SysRoleDTO role);
}
