package com.xinjian.common.service.location;

import com.xinjian.common.utils.ip.IpUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.lionsoul.ip2region.xdb.Searcher;
import org.springframework.stereotype.Service;

/**
 * 地理位置服务 (基于 ip2region)
 *
 * <p>特点：
 *
 * <ul>
 *   <li>使用 ip2region 本地数据库，性能极高且无需网络请求
 *   <li>支持 IPv4 地址查询
 *   <li>内存占用小，查询速度快
 *   <li>内网地址自动处理
 * </ul>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GeoLocationService {

  private final Searcher searcher;

  /**
   * 根据 IP 地址获取地理位置信息。
   *
   * @param ip IP 地址
   * @return GeoLocation 对象，查询失败则返回基础信息
   */
  public GeoLocation getGeoLocationByIp(String ip) {
    if (ip == null || ip.trim().isEmpty()) {
      return createUnknownLocation(ip);
    }

    // 使用重构后的 IpUtils 进行内部 IP 判断
    if (IpUtils.isInternalIp(ip)) {
      return createInternalLocation(ip);
    }

    // 如果 Searcher Bean 未能成功加载，则直接返回未知
    if (searcher == null) {
      log.warn("Ip2Region Searcher 未初始化，跳过 IP 解析");
      return createUnknownLocation(ip);
    }

    try {
      // 调用 searcher 进行查询
      String regionString = searcher.search(ip);
      // 使用 GeoLocation 的静态方法进行解析
      GeoLocation location = GeoLocation.fromRegionString(regionString);
      location.setOriginalIp(ip);
      return location;
    } catch (Exception e) {
      log.warn("通过 ip2region 解析 IP 地理位置异常，IP: [{}], 错误：{}", ip, e.getMessage());
      return createUnknownLocation(ip);
    }
  }

  /**
   * 根据 IP 地址获取简单的地理位置字符串（兼容旧方法）
   *
   * @param ip IP 地址
   * @return 地理位置字符串，格式： "省份 城市"
   * @deprecated 请使用 {@link #getGeoLocationByIp(String)} 方法
   */
  @Deprecated
  public String getRealAddressByIP(String ip) {
    GeoLocation location = getGeoLocationByIp(ip);
    return location.getSimpleLocation();
  }

  /**
   * 获取真实地址（兼容旧方法）
   *
   * @param ip IP 地址
   * @return 地理位置字符串
   * @deprecated 请使用 {@link #getGeoLocationByIp(String)} 方法
   */
  @Deprecated
  public String getRealAddress(String ip) {
    return getRealAddressByIP(ip);
  }

  /**
   * 创建内网 IP 的位置信息
   *
   * @param ip IP 地址
   * @return 内网位置信息
   */
  private GeoLocation createInternalLocation(String ip) {
    return GeoLocation.builder().originalIp(ip).city("内网 IP").build();
  }

  /**
   * 创建未知位置信息
   *
   * @param ip IP 地址
   * @return 未知位置信息
   */
  private GeoLocation createUnknownLocation(String ip) {
    return GeoLocation.builder().originalIp(ip).city("未知").build();
  }

  /**
   * 检查是否为内网 IP
   *
   * @param ip IP 地址
   * @return 是否为内网 IP
   * @deprecated 请使用 {@link IpUtils#isInternalIp(String)} 方法
   */
  @Deprecated
  public boolean isInnerIP(String ip) {
    return IpUtils.isInternalIp(ip);
  }
}
