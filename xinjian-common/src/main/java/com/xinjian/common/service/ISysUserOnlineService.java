package com.xinjian.common.service;

import com.xinjian.common.core.domain.entity.SysUserOnline;
import java.util.List;

/**
 * 系统在线用户服务接口
 *
 * <p>定义在线用户相关的核心业务操作，主要用于框架层的在线用户管理需求
 */
public interface ISysUserOnlineService {

  /**
   * 通过会话序号查询信息
   *
   * @param sessionId 会话 ID
   * @return 在线用户信息
   */
  SysUserOnline selectOnlineById(String sessionId);

  /**
   * 查询会话集合
   *
   * @param userOnline 在线用户对象
   * @return 在线用户集合
   */
  List<SysUserOnline> selectUserOnlineList(SysUserOnline userOnline);

  /**
   * 强制用户下线
   *
   * @param sessionId 会话 ID
   * @return 结果
   */
  int forceLogout(String sessionId);

  /**
   * 查询会话集合
   *
   * @param ipaddr IP 地址
   * @param userName 用户名
   * @return 在线用户集合
   */
  List<SysUserOnline> selectOnlineByExpired(String ipaddr, String userName);

  /**
   * 删除会话
   *
   * @param sessionId 会话 ID
   * @return 结果
   */
  int removeUserOnline(String sessionId);
}
