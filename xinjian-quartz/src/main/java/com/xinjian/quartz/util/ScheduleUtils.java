package com.xinjian.quartz.util;

import cn.hutool.extra.spring.SpringUtil;
import com.xinjian.common.constant.Constants;
import com.xinjian.common.constant.ScheduleConstants;
import com.xinjian.common.exception.status400.BadRequestException;
import com.xinjian.quartz.domain.SysJob;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;

/** 定时任务工具类 */
public class ScheduleUtils {
  /**
   * 得到 quartz 任务类
   *
   * @param sysJob 执行计划
   * @return 具体执行任务类
   */
  private static Class<? extends Job> getQuartzJobClass(SysJob sysJob) {
    boolean isConcurrent = "0".equals(sysJob.getConcurrent());
    return isConcurrent ? QuartzJobExecution.class : QuartzDisallowConcurrentExecution.class;
  }

  /** 构建任务触发对象 */
  public static TriggerKey getTriggerKey(Long jobId, String jobGroup) {
    return TriggerKey.triggerKey(ScheduleConstants.TASK_CLASS_NAME + jobId, jobGroup);
  }

  /** 构建任务键对象 */
  public static JobKey getJobKey(Long jobId, String jobGroup) {
    return JobKey.jobKey(ScheduleConstants.TASK_CLASS_NAME + jobId, jobGroup);
  }

  /** 创建定时任务 */
  public static void createScheduleJob(Scheduler scheduler, SysJob job) throws SchedulerException {
    Class<? extends Job> jobClass = getQuartzJobClass(job);
    // 构建 job 信息
    Long jobId = job.getJobId();
    String jobGroup = job.getJobGroup();
    JobDetail jobDetail =
        JobBuilder.newJob(jobClass).withIdentity(getJobKey(jobId, jobGroup)).build();

    // 表达式调度构建器
    CronScheduleBuilder cronScheduleBuilder =
        CronScheduleBuilder.cronSchedule(job.getCronExpression());
    cronScheduleBuilder = handleCronScheduleMisfirePolicy(job, cronScheduleBuilder);

    // 按新的 cronExpression 表达式构建一个新的 trigger
    CronTrigger trigger =
        TriggerBuilder.newTrigger()
            .withIdentity(getTriggerKey(jobId, jobGroup))
            .withSchedule(cronScheduleBuilder)
            .build();

    // 放入参数，运行时的方法可以获取
    jobDetail.getJobDataMap().put(ScheduleConstants.TASK_PROPERTIES, job);

    // 判断是否存在
    if (scheduler.checkExists(getJobKey(jobId, jobGroup))) {
      // 防止创建时存在数据问题 先移除，然后在执行创建操作
      scheduler.deleteJob(getJobKey(jobId, jobGroup));
    }

    // 判断任务是否过期
    if (Objects.nonNull(CronUtils.getNextExecution(job.getCronExpression()))) {
      // 执行调度任务
      scheduler.scheduleJob(jobDetail, trigger);
    }

    // 暂停任务
    if (!job.getStatus()) {
      scheduler.pauseJob(ScheduleUtils.getJobKey(jobId, jobGroup));
    }
  }

  /** 设置定时任务策略 */
  public static CronScheduleBuilder handleCronScheduleMisfirePolicy(
      SysJob job, CronScheduleBuilder cb) {
    switch (job.getMisfirePolicy()) {
      case ScheduleConstants.MISFIRE_DEFAULT:
        return cb;
      case ScheduleConstants.MISFIRE_IGNORE_MISFIRES:
        return cb.withMisfireHandlingInstructionIgnoreMisfires();
      case ScheduleConstants.MISFIRE_FIRE_AND_PROCEED:
        return cb.withMisfireHandlingInstructionFireAndProceed();
      case ScheduleConstants.MISFIRE_DO_NOTHING:
        return cb.withMisfireHandlingInstructionDoNothing();
      default:
        throw new BadRequestException(
            String.format("任务错误策略 '%s' 不能用于 cron 计划任务", job.getMisfirePolicy()));
    }
  }

  /**
   * 检查包名是否为白名单配置
   *
   * @param invokeTarget 目标字符串
   * @return 结果
   */
  public static boolean whiteList(String invokeTarget) {
    String packageName = StringUtils.substringBefore(invokeTarget, "(");
    int count = StringUtils.countMatches(packageName, ".");
    if (count > 1) {
      for (String whitelist : Constants.JOB_WHITELIST_STR) {
        if (invokeTarget.toLowerCase().contains(whitelist.toLowerCase())) {
          return true;
        }
      }
      return false;
    }
    Object obj = SpringUtil.getBean(StringUtils.split(invokeTarget, ".")[0]);
    String beanPackageName = obj.getClass().getPackage().getName();
    boolean isWhitelisted = false;
    for (String whitelist : Constants.JOB_WHITELIST_STR) {
      if (beanPackageName.toLowerCase().contains(whitelist.toLowerCase())) {
        isWhitelisted = true;
        break;
      }
    }
    boolean isError = false;
    for (String errorStr : Constants.JOB_ERROR_STR) {
      if (beanPackageName.toLowerCase().contains(errorStr.toLowerCase())) {
        isError = true;
        break;
      }
    }
    return isWhitelisted && !isError;
  }
}
