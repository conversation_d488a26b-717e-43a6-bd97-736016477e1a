package com.xinjian.quartz.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.common.core.page.TableDataInfo;
import com.xinjian.common.utils.poi.ExcelUtil;
import com.xinjian.quartz.domain.SysJobLog;
import com.xinjian.quartz.domain.query.SysJobLogQuery;
import com.xinjian.quartz.service.SysJobLogService;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/** 调度日志操作处理 */
@RestController
@RequestMapping("/monitor/jobLog")
public class SysJobLogController {
  @Autowired private SysJobLogService jobLogService;

  /** 查询定时任务调度日志列表 */
  @PreAuthorize("hasAnyAuthority('monitor:job:list')")
  @GetMapping("/list")
  public TableDataInfo<SysJobLog> list(SysJobLogQuery query) {
    IPage<SysJobLog> page = jobLogService.selectJobLogListByPage(query);
    return new TableDataInfo<>(page);
  }

  /** 导出定时任务调度日志列表 */
  @PreAuthorize("hasAnyAuthority('monitor:job:export')")
  // @Log(title = "任务调度日志", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, SysJobLog sysJobLog) {
    List<SysJobLog> list = jobLogService.selectJobLogList(sysJobLog);
    ExcelUtil<SysJobLog> util = new ExcelUtil<SysJobLog>(SysJobLog.class);
    util.exportExcel(response, list, "调度日志");
  }

  /** 根据调度编号获取详细信息 */
  @PreAuthorize("hasAnyAuthority('monitor:job:query')")
  @GetMapping(value = "/{jobLogId}")
  public SysJobLog getInfo(@PathVariable Long jobLogId) {
    return jobLogService.selectJobLogById(jobLogId);
  }

  /** 删除定时任务调度日志 */
  @PreAuthorize("hasAnyAuthority('monitor:job:remove')")
  // @Log(title = "定时任务调度日志", businessType = BusinessType.DELETE)
  @DeleteMapping("/{jobLogIds}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void remove(@PathVariable Long[] jobLogIds) {
    jobLogService.deleteJobLogByIds(jobLogIds);
  }

  /** 清空定时任务调度日志 */
  @PreAuthorize("hasAnyAuthority('monitor:job:remove')")
  // @Log(title = "调度日志", businessType = BusinessType.CLEAN)
  @DeleteMapping("/clean")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void clean() {
    jobLogService.cleanJobLog();
  }
}
