{
  "editor.codeLens": true,
  // JAVA ---------------------------------------------------------------------
  "files.exclude": {
    "**/.classpath": true,
    "**/.project": true,
    "**/.settings": true,
    "**/.factorypath": true
  },
  "java.configuration.updateBuildConfiguration": "automatic",
  "java.compile.nullAnalysis.mode": "automatic",
  "java.debug.settings.onBuildFailureProceed": true,
  "xml.format.enabled": false,
  "xml.format.splitAttributes": "preserve",
  "java.debug.settings.hotCodeReplace": "auto"
}
