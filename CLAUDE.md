# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于若依框架构建的 Java 后端服务 API 项目，采用模块化设计，专注于提供企业级后台管理系统解决方案。项目使用 Spring Boot 作为核心框架，集成了安全认证、数据访问、API 文档等企业级功能。

## 开发环境要求

- **Java**: 1.8+
- **Maven**: 3.6+
- **数据库**: MySQL/MariaDB/OceanBase
- **Redis**: 可选，用于缓存和会话管理

## 常用开发命令

### 构建和运行

```bash
# 清理并编译项目
mvn clean compile

# 打包项目
mvn clean package

# 运行项目（开发环境）
mvn spring-boot:run

# 使用指定环境打包
mvn clean package -P dev        # 开发环境
mvn clean package -P staging    # 测试环境
mvn clean package -P production # 生产环境
mvn clean package -P xc         # 信创环境（WAR 包）
```

### 代码格式化和检查

```bash
# 应用代码格式化
mvn spotless:apply

# 跳过 Git hooks 进行构建
mvn clean package -DskipGitHooks
```

### 生产环境部署

```bash
# 使用部署脚本（需要先构建）
./up.sh -m 512m -n xinjian-admin
```

## 项目架构

### 核心模块结构

- **xinjian-admin**: Web 服务入口，包含启动类和控制器
- **xinjian-common**: 通用工具模块，包含实体、DTO、工具类等
- **xinjian-framework**: 框架核心模块，包含安全配置、异常处理等
- **xinjian-generator**: 代码生成器模块
- **xinjian-quartz**: 定时任务模块
- **xinjian-starters**: 自定义 Starter 模块
  - **xinjian-starter-redis**: Redis 自动配置
  - **xinjian-starter-mybatis**: MyBatis 自动配置
- **xinjian-modules**: 功能模块
  - **xinjian-module-captcha**: 验证码模块

### 分层架构

项目采用标准的分层架构：

1. **Controller 层**: `xinjian-admin/src/main/java/com/xinjian/admin/web/`
2. **Service 层**: `xinjian-common/src/main/java/com/xinjian/common/service/`
3. **Mapper 层**: `xinjian-starter-mybatis/src/main/java/com/xinjian/starter/mybatis/mapper/`
4. **Entity 层**: `xinjian-common/src/main/java/com/xinjian/common/core/domain/entity/`

### 严格遵守的架构原则

1. Controller 层：只使用 DTO 或者 Request/Response/Query，完全不知道 Entity 的存在
2. Service 接口层：只使用 DTO 或者 Request/Response/Query，完全不知道 Entity 的存在，定义业务契约
3. Service 实现层：负责 DTO 或者 Request/Response/Query 和 Entity 的转换，处理业务逻辑，使用 mapstruct converter 进行转换
4. Mapper 层：只使用 Entity，负责数据持久化

### 关键设计模式

- **DTO 模式**: 使用 Data Transfer Object 在层间传输数据
- **Converter 模式**: 使用 MapStruct 进行实体与 DTO 之间的转换
- **统一异常处理**: 使用 Problem Spring Web 实现 RFC 7807 标准的错误处理
- **统一响应格式**: 直接返回 DTO 或者 Response

## 核心特性

### 安全认证

- 基于 Spring Security + JWT 的认证机制
- 支持角色权限和数据权限控制
- 集成验证码功能

### 数据访问

- 基于 MyBatis Plus 的数据访问层
- 支持多数据源配置
- 集成分页插件

### API 文档

- 集成 SpringDoc OpenAPI 3.0
- 自动生成 API 文档
- 访问地址：`http://localhost:8123/swagger-ui/index.html`

### 监控和日志

- 集成系统监控功能
- 使用 Logback 进行日志管理
- 支持 IP 地址定位和 User-Agent 解析

## 开发规范

### 包命名约定

- `com.xinjian.common.*`: 通用模块
- `com.xinjian.framework.*`: 框架模块
- `com.xinjian.admin.*`: 管理后台模块
- `com.xinjian.starter.*`: 自定义 Starter

### 代码生成

项目提供代码生成器，可以快速生成 CRUD 相关代码：

1. 访问 `/tool/gen` 页面
2. 选择数据表并配置生成参数
3. 生成并下载代码包

### 数据库设计

- 基础表结构在 `sql/basic.sql` 中定义
- 定时任务表在 `sql/quartz.sql` 中定义
- 支持多数据库类型（MySQL、MariaDB、OceanBase）

## 配置说明

### 环境配置

项目支持多环境配置，通过 Maven Profile 进行管理：

- `dev`: 开发环境（默认）
- `staging`: 测试环境
- `production`: 生产环境
- `xc`: 信创环境（生成 WAR 包）

### 核心配置文件

- `application.yml`: 基础配置
- `application-druid.yml`: 数据源配置
- `logback.xml`: 日志配置

## 构建产物

构建完成后会在 `target/` 目录下生成：

- JAR 包：`xinjian-admin_{environment}_{timestamp}.jar`
- WAR 包（仅信创环境）：`xinjian-admin.war`
- SBOM 文件：`cyclonedx/` 目录下的依赖清单

## 常见问题

### 启动失败

- 检查数据库连接配置
- 确认 Redis 连接（如果启用）
- 检查端口占用情况

### 代码生成问题

- 确保已配置正确的数据库连接
- 检查表是否有主键
- 确认用户有足够的权限访问系统表

### 权限问题

- 新增功能需要在 `sys_menu` 表中添加菜单记录
- 权限标识需要与 `@PreAuthorize` 注解中的值保持一致
