package com.xinjian.admin.demo.dto;

import com.xinjian.common.core.domain.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 岗位数据传输对象
 *
 * <p>用于岗位信息的展示和传输，包含岗位的基本信息。继承自 BaseDTO，包含基础的审计字段（创建时间、更新时间等）。
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PostDTO extends BaseDTO {
  private static final long serialVersionUID = 1L;

  /** 岗位 ID */
  private Long postId;

  /** 岗位编码 - 唯一标识 */
  private String postCode;

  /** 岗位名称 - 显示名称 */
  private String postName;

  /** 岗位排序 - 用于显示顺序 */
  private Integer postSort;

  /** 状态 - true 正常 false 停用 */
  private Boolean status;

  /** 用户是否存在此岗位标识 - 默认不存在 */
  private boolean flag = false;

  /** 岗位备注信息 */
  private String remark;
}
