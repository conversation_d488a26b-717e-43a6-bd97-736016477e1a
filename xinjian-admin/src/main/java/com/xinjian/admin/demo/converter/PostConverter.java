package com.xinjian.admin.demo.converter;

import com.xinjian.admin.demo.dto.PostDTO;
import com.xinjian.admin.demo.dto.PostRequest;
import com.xinjian.admin.demo.entity.Post;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * 岗位对象转换器
 *
 * <p>使用 MapStruct 自动生成对象转换的实现类，提供以下转换功能：
 *
 * <ul>
 *   <li>Entity 转 DTO - 用于数据展示
 *   <li>Request 转 Entity - 用于创建新记录
 *   <li>Request 更新 Entity - 用于更新现有记录
 * </ul>
 *
 * <p>配置说明：
 *
 * <ul>
 *   <li>componentModel = "spring" - 生成 Spring Bean
 *   <li>nullValuePropertyMappingStrategy = IGNORE - 忽略 null 值属性
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PostConverter {

  /**
   * 将 Entity 转换为 DTO
   *
   * @param entity 岗位实体
   * @return 岗位数据传输对象
   */
  PostDTO toDto(Post entity);

  /**
   * 将 Request 转换为 Entity（用于创建）
   *
   * <p>忽略 ID 和审计字段，让数据库自动生成
   *
   * @param request 岗位请求对象
   * @return 岗位实体
   */
  @Mapping(target = "postId", ignore = true)
  @Mapping(target = "createTime", ignore = true)
  @Mapping(target = "updateTime", ignore = true)
  @Mapping(target = "createBy", ignore = true)
  @Mapping(target = "updateBy", ignore = true)
  Post toEntity(PostRequest request);

  /**
   * 使用 Request 更新 Entity（用于更新）
   *
   * <p>忽略 ID 和审计字段，只更新可变字段
   *
   * @param request 岗位请求对象
   * @param entity 目标岗位实体
   */
  @Mapping(target = "postId", ignore = true)
  @Mapping(target = "createTime", ignore = true)
  @Mapping(target = "updateTime", ignore = true)
  @Mapping(target = "createBy", ignore = true)
  @Mapping(target = "updateBy", ignore = true)
  void updateEntity(PostRequest request, @MappingTarget Post entity);
}
