package com.xinjian.admin.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinjian.admin.demo.converter.PostConverter;
import com.xinjian.admin.demo.dto.PostDTO;
import com.xinjian.admin.demo.dto.PostQuery;
import com.xinjian.admin.demo.dto.PostRequest;
import com.xinjian.admin.demo.entity.Post;
import com.xinjian.admin.demo.mapper.PostMapper;
import com.xinjian.admin.demo.service.PostService;
import com.xinjian.common.exception.status404.ResourceNotFoundException;
import com.xinjian.common.properties.PaginationProperties;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

/**
 * 岗位服务实现类
 *
 * <p>实现了岗位相关的业务逻辑，包括：
 *
 * <ul>
 *   <li>岗位信息的查询和转换
 *   <li>岗位信息的创建、更新和删除
 *   <li>使用 LambdaQueryWrapper 构建动态查询条件
 *   <li>使用 MapStruct 进行对象转换
 *   <li>事务管理确保数据一致性
 * </ul>
 *
 * <p>事务说明：
 *
 * <ul>
 *   <li>查询方法：默认只读事务，提高性能
 *   <li>创建方法：REQUIRED 传播行为，开启新事务
 *   <li>更新方法：REQUIRED 传播行为，支持事务回滚
 *   <li>删除方法：REQUIRED 传播行为，确保数据完整性
 * </ul>
 *
 * <p>异常处理：
 *
 * <ul>
 *   <li>资源不存在：抛出 ResourceNotFoundException
 *   <li>数据验证：通过 @Valid 注解自动验证
 *   <li>数据库异常：由 Spring 事务管理器自动回滚
 * </ul>
 *
 * <p>该类继承自 ServiceImpl，可以直接使用 MyBatis-Plus 提供的通用方法。
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class PostServiceImpl extends ServiceImpl<PostMapper, Post> implements PostService {

  /** 岗位对象转换器 */
  private final PostConverter postConverter;

  /** 分页配置属性 */
  private final PaginationProperties paginationProperties;

  @Override
  public PostDTO selectPostById(Long postId) {
    log.debug("查询岗位信息，岗位 ID: {}", postId);
    Post entity = this.getById(postId);
    return postConverter.toDto(entity);
  }

  @Override
  public IPage<PostDTO> selectPostListByPage(PostQuery query) {
    log.debug("分页查询岗位列表，查询参数：{}", query);
    IPage<Post> page = query.buildPage(paginationProperties);
    LambdaQueryWrapper<Post> wrapper = buildQueryWrapper(query);
    List<Post> entityList = this.baseMapper.selectList(page, wrapper);
    page.setRecords(entityList);
    return page.convert(postConverter::toDto);
  }

  @Override
  @Transactional
  public void createPost(PostRequest request) {
    log.info("创建岗位，岗位信息：{}", request);
    Post entity = postConverter.toEntity(request);
    this.save(entity);
    log.info("岗位创建成功，岗位 ID: {}", entity.getPostId());
  }

  @Override
  @Transactional
  public void updatePost(Long postId, PostRequest request) {
    log.info("更新岗位，岗位 ID: {}, 更新信息：{}", postId, request);
    Post entity = this.getById(postId);
    if (entity == null) {
      log.warn("更新岗位失败，岗位不存在，岗位 ID: {}", postId);
      throw new ResourceNotFoundException("岗位不存在");
    }
    postConverter.updateEntity(request, entity);
    this.updateById(entity);
    log.info("岗位更新成功，岗位 ID: {}", postId);
  }

  @Override
  @Transactional
  public void deletePostById(Long postId) {
    log.info("删除岗位，岗位 ID: {}", postId);
    this.removeById(postId);
    log.info("岗位删除成功，岗位 ID: {}", postId);
  }

  @Override
  @Transactional
  public void deletePostByIds(List<Long> postIds) {
    if (postIds == null || postIds.isEmpty()) {
      log.warn("批量删除岗位失败，岗位 ID 列表为空");
      return;
    }
    log.info("批量删除岗位，岗位 ID 列表：{}", postIds);
    this.removeByIds(postIds);
    log.info("批量删除岗位成功，删除数量：{}", postIds.size());
  }

  /**
   * 构建查询条件
   *
   * <p>根据查询参数动态构建 LambdaQueryWrapper，支持模糊查询和精确查询
   *
   * @param query 查询参数
   * @return 构建好的查询条件包装器
   */
  private LambdaQueryWrapper<Post> buildQueryWrapper(PostQuery query) {
    LambdaQueryWrapper<Post> wrapper = new LambdaQueryWrapper<>();

    if (StringUtils.hasText(query.getPostCode())) {
      wrapper.like(Post::getPostCode, query.getPostCode());
    }
    if (StringUtils.hasText(query.getPostName())) {
      wrapper.like(Post::getPostName, query.getPostName());
    }
    if (query.getStatus() != null) {
      wrapper.eq(Post::getStatus, query.getStatus());
    }

    // 按排序号升序
    wrapper.orderByAsc(Post::getPostSort);

    return wrapper;
  }
}
