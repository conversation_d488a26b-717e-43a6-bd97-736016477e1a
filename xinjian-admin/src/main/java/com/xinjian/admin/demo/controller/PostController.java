package com.xinjian.admin.demo.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.demo.dto.PostDTO;
import com.xinjian.admin.demo.dto.PostQuery;
import com.xinjian.admin.demo.dto.PostRequest;
import com.xinjian.admin.demo.service.PostService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import javax.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/**
 * 岗位管理控制器
 *
 * <p>提供岗位信息的增删改查功能，包括：
 *
 * <ul>
 *   <li>获取单个岗位详情
 *   <li>分页查询岗位列表
 *   <li>创建新岗位
 *   <li>更新岗位信息
 *   <li>删除单个岗位
 *   <li>批量删除岗位
 * </ul>
 *
 * <p>所有接口均使用 RESTful 风格设计，返回标准的 HTTP 状态码。
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Tag(name = "岗位管理", description = "岗位信息管理接口")
@RestController
@RequestMapping("/demo/post")
public class PostController {

  /** 岗位服务层 */
  private final PostService postService;

  /**
   * 构造函数
   *
   * @param postService 岗位服务层
   */
  public PostController(PostService postService) {
    this.postService = postService;
  }

  /**
   * 获取岗位详细信息
   *
   * @param postId 岗位 ID
   * @return 岗位详细信息
   */
  @GetMapping("/{postId}")
  @Operation(summary = "获取岗位详细信息")
  public PostDTO getPost(@PathVariable Long postId) {
    return postService.selectPostById(postId);
  }

  /**
   * 分页查询岗位列表
   *
   * <p>支持按岗位编码、岗位名称、状态等条件进行筛选
   *
   * @param query 查询条件
   * @return 分页后的岗位列表
   */
  @GetMapping
  @Operation(summary = "分页查询岗位列表")
  public IPage<PostDTO> listPosts(PostQuery query) {
    return postService.selectPostListByPage(query);
  }

  /**
   * 创建岗位
   *
   * <p>创建新的岗位信息，岗位编码必须唯一
   *
   * @param request 岗位创建请求
   */
  @PostMapping
  @Operation(summary = "创建岗位")
  @ResponseStatus(HttpStatus.CREATED)
  public void createPost(@Valid @RequestBody PostRequest request) {
    postService.createPost(request);
  }

  /**
   * 更新岗位
   *
   * <p>更新指定岗位的信息，除岗位 ID 外的其他字段均可更新
   *
   * @param postId 岗位 ID
   * @param request 岗位更新请求
   */
  @PutMapping("/{postId}")
  @Operation(summary = "更新岗位")
  public void updatePost(
      @Parameter(description = "岗位 ID") @PathVariable Long postId,
      @Valid @RequestBody PostRequest request) {
    postService.updatePost(postId, request);
  }

  /**
   * 删除岗位
   *
   * <p>删除指定的岗位，删除后不可恢复
   *
   * @param postId 岗位 ID
   */
  @DeleteMapping("/{postId}")
  @Operation(summary = "删除岗位")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deletePost(@Parameter(description = "岗位 ID") @PathVariable Long postId) {
    postService.deletePostById(postId);
  }

  /**
   * 批量删除岗位
   *
   * <p>一次性删除多个岗位，删除后不可恢复
   *
   * @param postIds 岗位 ID 列表
   */
  @DeleteMapping("/batch")
  @Operation(summary = "批量删除岗位")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void batchDeletePosts(
      @Parameter(description = "岗位 ID 列表") @RequestBody List<Long> postIds) {
    postService.deletePostByIds(postIds);
  }
}
