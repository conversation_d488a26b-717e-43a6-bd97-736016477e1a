package com.xinjian.admin.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinjian.common.core.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 岗位实体类
 *
 * <p>对应数据库表 sys_post，用于存储系统中的岗位信息。包含岗位的基本信息、状态、审计字段等。
 *
 * <p>字段说明：
 *
 * <ul>
 *   <li>postId - 主键，自增
 *   <li>postCode - 岗位编码，唯一标识
 *   <li>postName - 岗位名称，显示用
 *   <li>postSort - 排序号，控制显示顺序
 *   <li>status - 状态，true 正常/false 停用
 *   <li>isDeleted - 逻辑删除标记，true 已删除/false 未删除
 * </ul>
 *
 * <p>逻辑删除说明：
 *
 * <ul>
 *   <li>使用逻辑删除而非物理删除，保证数据可追溯
 *   <li>查询时自动过滤已删除的记录
 *   <li>删除操作实际是更新 isDeleted 字段为 true
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Getter
@Setter
@ToString
@TableName("sys_post")
public class Post extends BaseEntity {
  /** 岗位 ID */
  @TableId(value = "post_id", type = IdType.AUTO)
  private Long postId;

  /** 岗位编码（唯一标识） */
  @TableField("post_code")
  private String postCode;

  /** 岗位名称（显示名称） */
  @TableField("post_name")
  private String postName;

  /** 显示顺序（排序号） */
  @TableField("post_sort")
  private Integer postSort;

  /** 岗位状态（1 正常 0 停用） */
  @TableField("status")
  private Boolean status;

  /** 岗位备注信息 */
  @TableField("remark")
  private String remark;

  /** 逻辑删除标记（true 已删除 false 未删除） */
  @TableField("is_deleted")
  @TableLogic
  private Boolean isDeleted;
}
