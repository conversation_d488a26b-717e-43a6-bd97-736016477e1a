package com.xinjian.admin.demo.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;

/**
 * 岗位请求对象
 *
 * <p>用于岗位创建和更新时的请求参数封装。包含完整的验证规则，确保数据的完整性和有效性。
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
public class PostRequest {

  /** 岗位编码 - 必填，最长 64 字符 */
  @NotBlank(message = "岗位编码不能为空")
  @Size(max = 64, message = "岗位编码长度不能超过 64 个字符")
  private String postCode;

  /** 岗位名称 - 必填，最长 50 字符 */
  @NotBlank(message = "岗位名称不能为空")
  @Size(max = 50, message = "岗位名称长度不能超过 50 个字符")
  private String postName;

  /** 岗位排序 - 必填，用于显示顺序 */
  @NotNull(message = "岗位排序不能为空")
  private Integer postSort;

  /** 岗位状态 - 可选，true 正常 false 停用 */
  private Boolean status;

  /** 岗位备注 - 可选，最长 500 字符 */
  @Size(max = 500, message = "备注长度不能超过 500 个字符")
  private String remark;
}
