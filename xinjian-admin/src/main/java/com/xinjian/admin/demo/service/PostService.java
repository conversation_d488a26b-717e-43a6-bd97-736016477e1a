package com.xinjian.admin.demo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xinjian.admin.demo.dto.PostDTO;
import com.xinjian.admin.demo.dto.PostQuery;
import com.xinjian.admin.demo.dto.PostRequest;
import com.xinjian.admin.demo.entity.Post;
import java.util.List;

/**
 * 岗位服务接口
 *
 * <p>提供岗位相关的业务逻辑处理，包括：
 *
 * <ul>
 *   <li>岗位信息的查询（单个查询、分页查询）
 *   <li>岗位信息的创建、更新、删除
 *   <li>岗位信息的批量删除
 * </ul>
 *
 * <p>该接口继承自 MyBatis-Plus 的 IService，提供了基础的 CRUD 操作。
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
public interface PostService extends IService<Post> {

  /**
   * 根据岗位 ID 查询岗位信息
   *
   * @param postId 岗位 ID
   * @return 岗位信息 DTO
   */
  PostDTO selectPostById(Long postId);

  /**
   * 分页查询岗位列表
   *
   * @param query 查询条件
   * @return 分页后的岗位列表
   */
  IPage<PostDTO> selectPostListByPage(PostQuery query);

  /**
   * 创建岗位
   *
   * @param request 岗位创建请求
   */
  void createPost(PostRequest request);

  /**
   * 更新岗位
   *
   * @param postId 岗位 ID
   * @param request 岗位更新请求
   */
  void updatePost(Long postId, PostRequest request);

  /**
   * 删除岗位
   *
   * @param postId 岗位 ID
   */
  void deletePostById(Long postId);

  /**
   * 批量删除岗位
   *
   * @param postIds 岗位 ID 列表
   */
  void deletePostByIds(List<Long> postIds);
}
