package com.xinjian.admin.demo.dto;

import com.xinjian.common.core.domain.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 岗位查询对象
 *
 * <p>用于岗位列表查询的参数封装，支持按不同条件筛选岗位。 继承自 BaseQuery，支持分页查询功能。
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PostQuery extends BaseQuery<PostQuery> {

  /** 岗位编码 - 支持模糊查询 */
  private String postCode;

  /** 岗位名称 - 支持模糊查询 */
  private String postName;

  /** 岗位状态 - 精确查询 */
  private Boolean status;

  @Override
  public PostQuery getQueryParams() {
    return this;
  }
}
