package com.xinjian.admin.web.dict;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.dict.mapper.SysDictDataMapper;
import com.xinjian.admin.web.dict.mapper.SysDictTypeMapper;
import com.xinjian.common.constant.CacheConstants;
import com.xinjian.common.core.domain.entity.SysDictData;
import com.xinjian.common.core.domain.entity.SysDictType;
import com.xinjian.common.core.domain.query.SysDictTypeQuery;
import com.xinjian.common.exception.status400.BadRequestException;
import com.xinjian.common.properties.PaginationProperties;
import com.xinjian.common.service.DictService;
import com.xinjian.common.service.ISysDictTypeService;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/** 字典 业务层处理 */
@Service
@RequiredArgsConstructor
public class SysDictTypeService implements DictService, ISysDictTypeService {
  private final SysDictTypeMapper dictTypeMapper;
  private final SysDictDataMapper dictDataMapper;
  private final PaginationProperties paginationProperties;

  @Autowired @Lazy private SysDictTypeService self;

  /**
   * 根据条件查询字典类型列表
   *
   * @param query 查询参数
   * @return 字典类型列表
   */
  @Override
  public List<SysDictType> selectDictTypeList(SysDictTypeQuery query) {
    return selectDictTypeListByQuery(query);
  }

  /**
   * 根据条件分页查询字典类型
   *
   * @param dictType 字典类型信息
   * @return 字典类型集合信息
   */
  public List<SysDictType> selectDictTypeList(SysDictType dictType) {
    return dictTypeMapper.selectDictTypeList(dictType);
  }

  /**
   * 根据条件分页查询字典类型（使用查询类）
   *
   * @param query 字典类型查询条件
   * @return 字典类型集合信息
   */
  public List<SysDictType> selectDictTypeListByQuery(SysDictTypeQuery query) {
    return dictTypeMapper.selectDictTypeListByQuery(query);
  }

  /**
   * 根据条件分页查询字典类型
   *
   * @param query 字典类型查询条件
   * @return 分页字典类型集合信息
   */
  public IPage<SysDictType> selectDictTypeListByPage(SysDictTypeQuery query) {

    IPage<SysDictType> page = query.buildPage(paginationProperties);

    // 调用 Mapper 方法查询数据
    List<SysDictType> records = dictTypeMapper.selectDictTypeListPage(page, query);
    page.setRecords(records);

    return page;
  }

  /**
   * 根据所有字典类型
   *
   * @return 字典类型集合信息
   */
  public List<SysDictType> selectDictTypeAll() {
    return dictTypeMapper.selectDictTypeAll();
  }

  /**
   * 根据字典类型查询字典数据
   *
   * @param dictType 字典类型
   * @return 字典数据集合信息
   */
  @Cacheable(
      cacheNames = CacheConstants.SYS_DICT_KEY,
      key = "#dictType",
      unless = "#result == null || #result.isEmpty()")
  public List<SysDictData> selectDictDataByType(String dictType) {
    List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(dictType);
    if (dictDatas != null && !dictDatas.isEmpty()) {
      return dictDatas.stream()
          .sorted(Comparator.comparing(SysDictData::getDictSort))
          .collect(Collectors.toList());
    }
    return null; // 返回 null 而不是空数组，避免缓存空结果
  }

  /**
   * 根据字典类型 ID 查询信息
   *
   * @param dictId 字典类型 ID
   * @return 字典类型
   */
  public SysDictType selectDictTypeById(Long dictId) {
    return dictTypeMapper.selectDictTypeById(dictId);
  }

  /**
   * 根据字典类型查询信息
   *
   * @param dictType 字典类型
   * @return 字典类型
   */
  public SysDictType selectDictTypeByType(String dictType) {
    return dictTypeMapper.selectDictTypeByType(dictType);
  }

  /**
   * 批量删除字典类型信息
   *
   * @param dictIds 需要删除的字典 ID
   */
  public void deleteDictTypeByIds(Long[] dictIds) {
    // 收集需要清除缓存的字典类型
    java.util.Set<String> dictTypes = new java.util.HashSet<>();

    for (Long dictId : dictIds) {
      SysDictType dictType = selectDictTypeById(dictId);
      if (dictDataMapper.countDictDataByType(dictType.getDictType()) > 0) {
        throw new BadRequestException(String.format("%s 已分配，不能删除", dictType.getDictName()));
      }
      dictTypes.add(dictType.getDictType());
      dictTypeMapper.deleteDictTypeById(dictId);
    }

    // 清除受影响的字典类型缓存
    for (String dictType : dictTypes) {
      evictDictCache(dictType);
    }
  }

  /** 加载字典缓存数据 */
  @Override
  public void loadingDictCache() {
    // 获取所有字典类型
    List<SysDictType> dictTypes = selectDictTypeAll();

    // 预热所有字典类型的缓存
    for (SysDictType dictType : dictTypes) {
      try {
        // 必须通过注入的 self 代理对象来调用，才能触发 AOP
        self.selectDictDataByType(dictType.getDictType());
      } catch (Exception e) {
        // 记录错误但不中断预热过程
        System.err.println("预热字典缓存失败：" + dictType.getDictType() + ", 错误：" + e.getMessage());
      }
    }
  }

  /**
   * 清除指定字典类型的缓存
   *
   * @param dictType 字典类型
   */
  @CacheEvict(cacheNames = CacheConstants.SYS_DICT_KEY, key = "#dictType")
  public void evictDictCache(String dictType) {
    // 空方法，仅用于清除缓存
  }

  /** 清空字典缓存数据 */
  @Override
  @CacheEvict(cacheNames = CacheConstants.SYS_DICT_KEY, allEntries = true)
  public void clearDictCache() {
    // 通过注解清空所有缓存
  }

  /** 重置字典缓存数据 */
  public void resetDictCache() {
    clearDictCache();
    loadingDictCache();
  }

  /**
   * 新增保存字典类型信息
   *
   * @param dict 字典类型信息
   * @return 结果
   */
  @CacheEvict(cacheNames = CacheConstants.SYS_DICT_KEY, key = "#dict.dictType")
  public int insertDictType(SysDictType dict) {
    return dictTypeMapper.insertDictType(dict);
  }

  /**
   * 修改保存字典类型信息
   *
   * @param dict 字典类型信息
   * @return 结果
   */
  @Transactional
  @CacheEvict(cacheNames = CacheConstants.SYS_DICT_KEY, key = "#dict.dictType")
  public int updateDictType(SysDictType dict) {
    SysDictType oldDict = dictTypeMapper.selectDictTypeById(dict.getDictId());
    if (!Objects.equals(oldDict.getDictType(), dict.getDictType())) {
      // 清理旧字典类型的缓存，@CacheEvict 会处理新类型的缓存
      // 由于 Spring Cache 无法根据动态值清理缓存，这里需要使用反射方式清理
      // 或者直接清空所有缓存
      clearDictCache();
    }
    dictDataMapper.updateDictDataType(oldDict.getDictType(), dict.getDictType());
    return dictTypeMapper.updateDictType(dict);
  }

  /**
   * 校验字典类型称是否唯一
   *
   * @param dict 字典类型
   * @return 结果
   */
  public boolean checkDictTypeUnique(SysDictType dict) {
    Long dictId = Objects.isNull(dict.getDictId()) ? -1L : dict.getDictId();
    SysDictType dictType = dictTypeMapper.checkDictTypeUnique(dict.getDictType());
    if (Objects.nonNull(dictType) && dictType.getDictId().longValue() != dictId.longValue()) {
      return false;
    }
    return true;
  }
}
