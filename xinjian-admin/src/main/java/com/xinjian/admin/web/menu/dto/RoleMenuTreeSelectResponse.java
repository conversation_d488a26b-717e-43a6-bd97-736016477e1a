package com.xinjian.admin.web.menu.dto;

import com.xinjian.common.core.domain.TreeSelect;
import java.util.List;

/** 角色菜单树选择响应对象 */
public class RoleMenuTreeSelectResponse {

  private List<Long> checkedKeys;
  private List<TreeSelect> menus;

  public RoleMenuTreeSelectResponse() {}

  public RoleMenuTreeSelectResponse(List<Long> checkedKeys, List<TreeSelect> menus) {
    this.checkedKeys = checkedKeys;
    this.menus = menus;
  }

  public List<Long> getCheckedKeys() {
    return checkedKeys;
  }

  public void setCheckedKeys(List<Long> checkedKeys) {
    this.checkedKeys = checkedKeys;
  }

  public List<TreeSelect> getMenus() {
    return menus;
  }

  public void setMenus(List<TreeSelect> menus) {
    this.menus = menus;
  }
}
