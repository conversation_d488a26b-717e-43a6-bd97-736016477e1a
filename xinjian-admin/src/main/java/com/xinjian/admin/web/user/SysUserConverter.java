package com.xinjian.admin.web.user;

import com.xinjian.admin.web.role.entity.SysRole;
import com.xinjian.admin.web.user.entity.SysUser;
import com.xinjian.common.core.domain.dto.SysDeptDTO;
import com.xinjian.common.core.domain.dto.SysRoleDTO;
import com.xinjian.common.core.domain.dto.SysUserDTO;
import com.xinjian.common.core.domain.entity.SysDept;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SysUserConverter {

  @Mapping(target = "password", source = "password")
  SysUserDTO toDto(SysUser entity);

  @Mapping(target = "remark", ignore = true)
  @Mapping(target = "searchValue", ignore = true)
  @Mapping(target = "deptId", ignore = true)
  @Mapping(target = "isDeleted", ignore = true)
  @Mapping(target = "password", ignore = true)
  SysUser toEntity(SysUserDTO dto);

  @Mapping(target = "remark", ignore = true)
  @Mapping(target = "searchValue", ignore = true)
  @Mapping(target = "deptId", ignore = true)
  @Mapping(target = "isDeleted", ignore = true)
  @Mapping(target = "password", ignore = true)
  void updateEntity(SysUserDTO dto, @MappingTarget SysUser entity);

  SysDeptDTO toDeptDto(SysDept entity);

  SysRoleDTO toRoleDto(SysRole entity);

  List<SysRoleDTO> toRoleDtoList(List<SysRole> entities);
}
