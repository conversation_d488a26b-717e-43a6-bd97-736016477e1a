package com.xinjian.admin.web.notice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xinjian.admin.web.notice.dto.SysNoticeDTO;
import com.xinjian.admin.web.notice.dto.SysNoticeQuery;
import com.xinjian.admin.web.notice.dto.SysNoticeRequest;
import com.xinjian.admin.web.notice.entity.SysNotice;

public interface SysNoticeService extends IService<SysNotice> {

  SysNoticeDTO selectNoticeById(Long noticeId);

  IPage<SysNoticeDTO> selectNoticeListByPage(SysNoticeQuery query);

  void createNotice(SysNoticeRequest request);

  void updateNotice(Long noticeId, SysNoticeRequest request);

  void deleteNoticeById(Long noticeId);
}
