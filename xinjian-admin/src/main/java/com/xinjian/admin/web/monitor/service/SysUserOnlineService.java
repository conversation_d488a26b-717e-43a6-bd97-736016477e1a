package com.xinjian.admin.web.monitor.service;

import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.dto.UserSessionInfo;
import com.xinjian.common.core.domain.entity.SysUserOnline;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/** 在线用户 服务层 */
@Service
public class SysUserOnlineService {
  /**
   * 通过登录地址查询信息
   *
   * @param ipaddr 登录地址
   * @param user 用户信息
   * @param sessionInfo 用户会话信息
   * @return 在线用户信息
   */
  public SysUserOnline selectOnlineByIpaddr(
      String ipaddr, LoginUser user, UserSessionInfo sessionInfo) {
    if (StringUtils.equals(ipaddr, sessionInfo.getIpaddr())) {
      return loginUserToUserOnline(user, sessionInfo);
    }
    return null;
  }

  /**
   * 通过用户名称查询信息
   *
   * @param userName 用户名称
   * @param user 用户信息
   * @param sessionInfo 用户会话信息
   * @return 在线用户信息
   */
  public SysUserOnline selectOnlineByUserName(
      String userName, LoginUser user, UserSessionInfo sessionInfo) {
    if (StringUtils.equals(userName, user.getUsername())) {
      return loginUserToUserOnline(user, sessionInfo);
    }
    return null;
  }

  /**
   * 通过登录地址/用户名称查询信息
   *
   * @param ipaddr 登录地址
   * @param userName 用户名称
   * @param user 用户信息
   * @param sessionInfo 用户会话信息
   * @return 在线用户信息
   */
  public SysUserOnline selectOnlineByInfo(
      String ipaddr, String userName, LoginUser user, UserSessionInfo sessionInfo) {
    if (StringUtils.equals(ipaddr, sessionInfo.getIpaddr())
        && StringUtils.equals(userName, user.getUsername())) {
      return loginUserToUserOnline(user, sessionInfo);
    }
    return null;
  }

  /**
   * 设置在线用户信息
   *
   * @param user 登录用户信息
   * @param sessionInfo 用户会话信息
   * @return 在线用户
   */
  public SysUserOnline loginUserToUserOnline(LoginUser user, UserSessionInfo sessionInfo) {
    if (Objects.isNull(user) || Objects.isNull(sessionInfo)) {
      return null;
    }
    SysUserOnline sysUserOnline = new SysUserOnline();
    sysUserOnline.setTokenId(sessionInfo.getToken());
    sysUserOnline.setUserName(user.getUsername());
    sysUserOnline.setIpaddr(sessionInfo.getIpaddr());
    sysUserOnline.setLoginLocation(sessionInfo.getLoginLocation());
    sysUserOnline.setBrowser(sessionInfo.getBrowser());
    sysUserOnline.setOs(sessionInfo.getOs());
    sysUserOnline.setLoginTime(sessionInfo.getLoginTime());
    sysUserOnline.setDeptName(sessionInfo.getUsername()); // 使用用户名作为部门名称，或者需要从其他地方获取
    return sysUserOnline;
  }
}
