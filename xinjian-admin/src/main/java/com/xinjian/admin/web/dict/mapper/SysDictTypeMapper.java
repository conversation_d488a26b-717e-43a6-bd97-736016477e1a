package com.xinjian.admin.web.dict.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.common.core.domain.entity.SysDictType;
import com.xinjian.common.core.domain.query.SysDictTypeQuery;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/** 字典表 数据层 */
public interface SysDictTypeMapper extends BaseMapper<SysDictType> {
  /**
   * 根据条件分页查询字典类型
   *
   * @param dictType 字典类型信息
   * @return 字典类型集合信息
   */
  public List<SysDictType> selectDictTypeList(SysDictType dictType);

  /**
   * 根据条件分页查询字典类型（使用查询类）
   *
   * @param query 字典类型查询条件
   * @return 字典类型集合信息
   */
  public List<SysDictType> selectDictTypeListByQuery(SysDictTypeQuery query);

  /**
   * 根据条件分页查询字典类型
   *
   * @param page 分页对象
   * @param query 字典类型查询条件
   * @return 当页的数据列表。分页的总数等信息，由分页插件填充到传入的 page 对象中。
   */
  public List<SysDictType> selectDictTypeListPage(
      @Param("page") IPage<SysDictType> page, @Param("query") SysDictTypeQuery query);

  /**
   * 根据所有字典类型
   *
   * @return 字典类型集合信息
   */
  public List<SysDictType> selectDictTypeAll();

  /**
   * 根据字典类型 ID 查询信息
   *
   * @param dictId 字典类型 ID
   * @return 字典类型
   */
  public SysDictType selectDictTypeById(Long dictId);

  /**
   * 根据字典类型查询信息
   *
   * @param dictType 字典类型
   * @return 字典类型
   */
  public SysDictType selectDictTypeByType(String dictType);

  /**
   * 通过字典 ID 删除字典信息
   *
   * @param dictId 字典 ID
   * @return 结果
   */
  public int deleteDictTypeById(Long dictId);

  /**
   * 批量删除字典类型信息
   *
   * @param dictIds 需要删除的字典 ID
   * @return 结果
   */
  public int deleteDictTypeByIds(Long[] dictIds);

  /**
   * 新增字典类型信息
   *
   * @param dictType 字典类型信息
   * @return 结果
   */
  public int insertDictType(SysDictType dictType);

  /**
   * 修改字典类型信息
   *
   * @param dictType 字典类型信息
   * @return 结果
   */
  public int updateDictType(SysDictType dictType);

  /**
   * 校验字典类型称是否唯一
   *
   * @param dictType 字典类型
   * @return 结果
   */
  public SysDictType checkDictTypeUnique(String dictType);
}
