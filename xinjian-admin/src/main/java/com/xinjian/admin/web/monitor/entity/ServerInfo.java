package com.xinjian.admin.web.monitor.entity;

import com.xinjian.common.utils.TimeUtils;
import com.xinjian.common.utils.ip.IpUtils;
import java.lang.management.ManagementFactory;
import java.math.BigDecimal;
import java.net.UnknownHostException;
import java.util.LinkedList;
import java.util.List;
import java.util.Properties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.CentralProcessor.TickType;
import oshi.hardware.GlobalMemory;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.software.os.FileSystem;
import oshi.software.os.OSFileStore;
import oshi.software.os.OperatingSystem;
import oshi.util.Util;

/** 服务器相关信息 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServerInfo {
  private static final int OSHI_WAIT_SECOND = 1000;

  /** CPU 相关信息 */
  private Cpu cpu = new Cpu();

  /** 內存相关信息 */
  private Mem mem = new Mem();

  /** JVM 相关信息 */
  private Jvm jvm = new Jvm();

  /** 服务器相关信息 */
  private Sys sys = new Sys();

  /** 磁盘相关信息 */
  private List<SysFile> sysFiles = new LinkedList<SysFile>();

  /** 设置磁盘信息 */
  private void setSystemFiles(OperatingSystem os) {
    FileSystem fileSystem = os.getFileSystem();
    List<OSFileStore> fsArray = fileSystem.getFileStores();
    for (OSFileStore fs : fsArray) {
      long free = fs.getUsableSpace();
      long total = fs.getTotalSpace();
      long used = total - free;
      SysFile sysFile = new SysFile();
      sysFile.setDirName(fs.getMount());
      sysFile.setSysTypeName(fs.getType());
      sysFile.setTypeName(fs.getName());
      sysFile.setTotal(convertFileSize(total));
      sysFile.setFree(convertFileSize(free));
      sysFile.setUsed(convertFileSize(used));
      sysFile.setUsage(
          BigDecimal.valueOf(used)
              .divide(BigDecimal.valueOf(total), 4, BigDecimal.ROUND_HALF_UP)
              .multiply(BigDecimal.valueOf(100))
              .doubleValue());
      sysFiles.add(sysFile);
    }
  }

  public void copyTo() throws Exception {
    SystemInfo si = new SystemInfo();
    HardwareAbstractionLayer hal = si.getHardware();

    setCpuInfo(hal.getProcessor());

    setMemInfo(hal.getMemory());

    setSysInfo();

    setJvmInfo();

    setSystemFiles(si.getOperatingSystem());
  }

  /** 设置 CPU 信息 */
  private void setCpuInfo(CentralProcessor processor) {
    // CPU 信息
    long[] prevTicks = processor.getSystemCpuLoadTicks();
    Util.sleep(OSHI_WAIT_SECOND);
    long[] ticks = processor.getSystemCpuLoadTicks();
    long nice = ticks[TickType.NICE.getIndex()] - prevTicks[TickType.NICE.getIndex()];
    long irq = ticks[TickType.IRQ.getIndex()] - prevTicks[TickType.IRQ.getIndex()];
    long softirq = ticks[TickType.SOFTIRQ.getIndex()] - prevTicks[TickType.SOFTIRQ.getIndex()];
    long steal = ticks[TickType.STEAL.getIndex()] - prevTicks[TickType.STEAL.getIndex()];
    long cSys = ticks[TickType.SYSTEM.getIndex()] - prevTicks[TickType.SYSTEM.getIndex()];
    long user = ticks[TickType.USER.getIndex()] - prevTicks[TickType.USER.getIndex()];
    long iowait = ticks[TickType.IOWAIT.getIndex()] - prevTicks[TickType.IOWAIT.getIndex()];
    long idle = ticks[TickType.IDLE.getIndex()] - prevTicks[TickType.IDLE.getIndex()];
    long totalCpu = user + nice + cSys + idle + iowait + irq + softirq + steal;
    cpu.setCpuNum(processor.getLogicalProcessorCount());
    cpu.setTotal(totalCpu);
    cpu.setSys(cSys);
    cpu.setUsed(user);
    cpu.setWait(iowait);
    cpu.setFree(idle);
  }

  /** 设置内存信息 */
  private void setMemInfo(GlobalMemory memory) {
    mem.setTotal(memory.getTotal());
    mem.setUsed(memory.getTotal() - memory.getAvailable());
    mem.setFree(memory.getAvailable());
  }

  /** 设置服务器信息 */
  private void setSysInfo() {
    Properties props = System.getProperties();
    sys.setComputerName(IpUtils.getHostName());
    sys.setComputerIp(IpUtils.getHostIp());
    sys.setOsName(props.getProperty("os.name"));
    sys.setOsArch(props.getProperty("os.arch"));
    sys.setUserDir(props.getProperty("user.dir"));
  }

  /** 设置 Java 虚拟机 */
  private void setJvmInfo() throws UnknownHostException {
    Properties props = System.getProperties();
    jvm.setTotal(Runtime.getRuntime().totalMemory());
    jvm.setMax(Runtime.getRuntime().maxMemory());
    jvm.setFree(Runtime.getRuntime().freeMemory());
    jvm.setVersion(props.getProperty("java.version"));
    jvm.setHome(props.getProperty("java.home"));
  }

  /**
   * 字节转换
   *
   * @param size 字节大小
   * @return 转换后值
   */
  public String convertFileSize(long size) {
    long kb = 1024;
    long mb = kb * 1024;
    long gb = mb * 1024;
    if (size >= gb) {
      return String.format("%.1f GB", (float) size / gb);
    } else if (size >= mb) {
      float f = (float) size / mb;
      return String.format(f > 100 ? "%.0f MB" : "%.1f MB", f);
    } else if (size >= kb) {
      float f = (float) size / kb;
      return String.format(f > 100 ? "%.0f KB" : "%.1f KB", f);
    } else {
      return String.format("%d B", size);
    }
  }

  /** CPU 相关信息 */
  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Cpu {
    /** 核心数 */
    private int cpuNum;

    /** CPU 总的使用率 */
    private double total;

    /** CPU 系统使用率 */
    private double sys;

    /** CPU 用户使用率 */
    private double used;

    /** CPU 当前等待率 */
    private double wait;

    /** CPU 当前空闲率 */
    private double free;

    public double getTotal() {
      return BigDecimal.valueOf(total * 100).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    public double getSys() {
      return BigDecimal.valueOf(sys * 100)
          .divide(BigDecimal.valueOf(total), 2, BigDecimal.ROUND_HALF_UP)
          .doubleValue();
    }

    public double getUsed() {
      return BigDecimal.valueOf(used * 100)
          .divide(BigDecimal.valueOf(total), 2, BigDecimal.ROUND_HALF_UP)
          .doubleValue();
    }

    public double getWait() {
      return BigDecimal.valueOf(wait * 100)
          .divide(BigDecimal.valueOf(total), 2, BigDecimal.ROUND_HALF_UP)
          .doubleValue();
    }

    public double getFree() {
      return BigDecimal.valueOf(free * 100)
          .divide(BigDecimal.valueOf(total), 2, BigDecimal.ROUND_HALF_UP)
          .doubleValue();
    }
  }

  /** 內存相关信息 */
  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Mem {
    /** 内存总量 */
    private double total;

    /** 已用内存 */
    private double used;

    /** 剩余内存 */
    private double free;

    public double getTotal() {
      return BigDecimal.valueOf(total)
          .divide(BigDecimal.valueOf(1024 * 1024 * 1024), 2, BigDecimal.ROUND_HALF_UP)
          .doubleValue();
    }

    public double getUsed() {
      return BigDecimal.valueOf(used)
          .divide(BigDecimal.valueOf(1024 * 1024 * 1024), 2, BigDecimal.ROUND_HALF_UP)
          .doubleValue();
    }

    public double getFree() {
      return BigDecimal.valueOf(free)
          .divide(BigDecimal.valueOf(1024 * 1024 * 1024), 2, BigDecimal.ROUND_HALF_UP)
          .doubleValue();
    }

    public double getUsage() {
      return BigDecimal.valueOf(used)
          .divide(BigDecimal.valueOf(total), 4, BigDecimal.ROUND_HALF_UP)
          .multiply(BigDecimal.valueOf(100))
          .doubleValue();
    }
  }

  /** JVM 相关信息 */
  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Jvm {
    /** 当前 JVM 占用的内存总数 (M) */
    private double total;

    /** JVM 最大可用内存总数 (M) */
    private double max;

    /** JVM 空闲内存 (M) */
    private double free;

    /** JDK 版本 */
    private String version;

    /** JDK 路径 */
    private String home;

    public double getTotal() {
      return BigDecimal.valueOf(total)
          .divide(BigDecimal.valueOf(1024 * 1024), 2, BigDecimal.ROUND_HALF_UP)
          .doubleValue();
    }

    public double getMax() {
      return BigDecimal.valueOf(max)
          .divide(BigDecimal.valueOf(1024 * 1024), 2, BigDecimal.ROUND_HALF_UP)
          .doubleValue();
    }

    public double getFree() {
      return BigDecimal.valueOf(free)
          .divide(BigDecimal.valueOf(1024 * 1024), 2, BigDecimal.ROUND_HALF_UP)
          .doubleValue();
    }

    public double getUsed() {
      return BigDecimal.valueOf(total - free)
          .divide(BigDecimal.valueOf(1024 * 1024), 2, BigDecimal.ROUND_HALF_UP)
          .doubleValue();
    }

    public double getUsage() {
      return BigDecimal.valueOf(total - free)
          .divide(BigDecimal.valueOf(total), 4, BigDecimal.ROUND_HALF_UP)
          .multiply(BigDecimal.valueOf(100))
          .doubleValue();
    }

    /** 获取 JDK 名称 */
    public String getName() {
      return ManagementFactory.getRuntimeMXBean().getVmName();
    }

    /** JDK 启动时间 */
    public String getStartTime() {
      return TimeUtils.format("yyyy-MM-dd HH:mm:ss", TimeUtils.getServerStartDate());
    }

    /** JDK 运行时间 */
    public String getRunTime() {
      return TimeUtils.formatDuration(TimeUtils.getServerStartDate(), TimeUtils.now());
    }

    /** 运行参数 */
    public String getInputArgs() {
      return ManagementFactory.getRuntimeMXBean().getInputArguments().toString();
    }
  }

  /** 系统相关信息 */
  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Sys {
    /** 服务器名称 */
    private String computerName;

    /** 服务器 Ip */
    private String computerIp;

    /** 项目路径 */
    private String userDir;

    /** 操作系统 */
    private String osName;

    /** 系统架构 */
    private String osArch;
  }

  /** 系统文件相关信息 */
  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class SysFile {
    /** 盘符路径 */
    private String dirName;

    /** 盘符类型 */
    private String sysTypeName;

    /** 文件类型 */
    private String typeName;

    /** 总大小 */
    private String total;

    /** 剩余大小 */
    private String free;

    /** 已经使用量 */
    private String used;

    /** 资源的使用率 */
    private double usage;
  }
}
