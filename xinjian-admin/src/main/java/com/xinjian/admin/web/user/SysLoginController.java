package com.xinjian.admin.web.user;

import com.xinjian.admin.web.user.dto.LoginResponse;
import com.xinjian.admin.web.user.dto.UserInfoResponse;
import com.xinjian.common.core.domain.dto.LoginRequest;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.dto.SysUserDTO;
import com.xinjian.common.core.domain.entity.SysMenu;
import com.xinjian.common.service.ISysMenuService;
import com.xinjian.common.service.ISysUserService;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

/** 登录验证 */
@RestController
@RequestMapping("")
public class SysLoginController {
  @Autowired private SysLoginService loginService;

  @Autowired private ISysMenuService menuService;

  @Autowired private ISysUserService userService;

  /**
   * 登录方法
   *
   * @param loginBody 登录信息
   * @return 结果
   */
  @PostMapping("/login")
  public LoginResponse login(@RequestBody LoginRequest loginBody) {
    // 生成令牌
    String token =
        loginService.login(
            loginBody.getUsername(),
            loginBody.getPassword(),
            loginBody.getCode(),
            loginBody.getUuid());
    return new LoginResponse(token);
  }

  /**
   * 获取当前登录用户的详细信息、角色和权限
   *
   * @param loginUser Spring Security 自动注入的当前登录用户凭证对象
   * @return 包含用户详细信息、角色和权限的响应体
   */
  @GetMapping("getInfo")
  public UserInfoResponse getInfo(@AuthenticationPrincipal LoginUser loginUser) {

    // 1. 获取用于 UI 展示的用户详细信息（如昵称、邮箱、头像等）需要用 SysUserDTO，因为 LoginUser 主要用于认证，可能不包含所有展示性字段
    SysUserDTO user = userService.selectUserById(loginUser.getUserId());

    // 2. 直接从 loginUser 对象中获取角色和权限，无需再调用 permissionService，因为这些信息在登录时已经加载并缓存好了
    Set<String> roles = loginUser.getRoles();
    Set<String> permissions = loginUser.getPermissions();

    // 3. 组装并返回响应对象
    return new UserInfoResponse(user, roles, permissions);
  }

  /**
   * 获取路由信息
   *
   * @return 路由信息
   */
  @GetMapping("getRouters")
  public Object getRouters(@AuthenticationPrincipal LoginUser loginUser) {
    Long userId = loginUser.getUserId();
    List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
    return menuService.buildMenus(menus, "");
  }
}
