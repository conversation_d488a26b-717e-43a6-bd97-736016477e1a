package com.xinjian.admin.web.user;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.dept.SysDeptService;
import com.xinjian.admin.web.role.SysRoleService;
import com.xinjian.common.core.domain.TreeSelect;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.dto.SysUserDTO;
import com.xinjian.common.core.domain.query.SysDeptQuery;
import com.xinjian.common.core.domain.query.SysUserQuery;
import com.xinjian.common.core.page.TableDataInfo;
import com.xinjian.common.exception.status400.BadRequestException;
import com.xinjian.common.exception.status409.ConflictException;
import com.xinjian.common.utils.SecurityUtils;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/** 用户信息 */
@RestController
@RequestMapping("/system/user")
public class SysUserController {
  @Autowired private SysUserService userService;

  @Autowired private SysRoleService roleService;

  @Autowired private SysDeptService deptService;

  /** 获取用户列表 */
  @PreAuthorize("hasAnyAuthority('system:user:list')")
  @GetMapping("/list")
  public TableDataInfo<SysUserDTO> list(SysUserQuery query) {
    IPage<SysUserDTO> page = userService.selectUserListByPage(query);
    return new TableDataInfo<>(page);
  }

  // @Log(title = "用户管理", businessType = BusinessType.EXPORT)
  @PreAuthorize("hasAnyAuthority('system:user:export')")
  @PostMapping("/export")
  public void export(HttpServletResponse response, SysUserQuery query) {
    userService.export(response, query);
  }

  // @Log(title = "用户管理", businessType = BusinessType.IMPORT)
  @PreAuthorize("hasAnyAuthority('system:user:import')")
  @PostMapping("/importData")
  @ResponseStatus(HttpStatus.CREATED)
  public String importData(
      MultipartFile file, boolean updateSupport, @AuthenticationPrincipal LoginUser loginUser)
      throws Exception {
    return userService.importData(file, updateSupport, loginUser.getUsername());
  }

  @PostMapping("/importTemplate")
  public void importTemplate(HttpServletResponse response) {
    userService.importTemplate(response);
  }

  /** 根据用户编号获取详细信息 */
  @PreAuthorize("hasAnyAuthority('system:user:query')")
  @GetMapping(value = {"/", "/{userId}"})
  public SysUserDTO getInfo(@PathVariable(required = false) Long userId) {
    userService.checkUserDataScope(userId, SecurityUtils.getUserId());
    return userService.selectUserById(userId);
  }

  /** 新增用户 */
  @PreAuthorize("hasAnyAuthority('system:user:add')")
  // @Log(title = "用户管理", businessType = BusinessType.INSERT)
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public SysUserDTO add(
      @Validated @RequestBody SysUserDTO user,
      @AuthenticationPrincipal LoginUser loginUser,
      @RequestParam(required = false) Long[] roleIds) {
    if (roleIds != null) {
      for (Long roleId : roleIds) {
        roleService.checkRoleDataScope(loginUser.getUserId(), roleId);
      }
    }
    if (!userService.checkUserNameUnique(user)) {
      throw new ConflictException("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
    } else if (StringUtils.isNotEmpty(user.getMobile()) && !userService.checkMobileUnique(user)) {
      throw new ConflictException("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
    } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
      throw new ConflictException("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
    }
    userService.insertUser(user);
    // 如果有角色 ID，需要单独处理角色关联
    if (roleIds != null && roleIds.length > 0) {
      userService.insertUserAuthWithRoleIds(user, roleIds);
    }
    return userService.selectUserById(user.getUserId());
  }

  /** 修改用户 */
  @PreAuthorize("hasAnyAuthority('system:user:edit')")
  // @Log(title = "用户管理", businessType = BusinessType.UPDATE)
  @PutMapping
  public SysUserDTO edit(
      @Validated @RequestBody SysUserDTO user,
      @AuthenticationPrincipal LoginUser loginUser,
      @RequestParam(required = false) Long[] roleIds) {
    userService.checkUserAllowed(user);
    userService.checkUserDataScope(user.getUserId(), loginUser.getUserId());
    if (roleIds != null) {
      for (Long roleId : roleIds) {
        roleService.checkRoleDataScope(loginUser.getUserId(), roleId);
      }
    }
    if (!userService.checkUserNameUnique(user)) {
      throw new ConflictException("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
    } else if (StringUtils.isNotEmpty(user.getMobile()) && !userService.checkMobileUnique(user)) {
      throw new ConflictException("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
    } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
      throw new ConflictException("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
    }
    userService.updateUser(user);
    // 如果有角色 ID，需要单独处理角色关联
    if (roleIds != null) {
      userService.insertUserAuthWithRoleIds(user, roleIds);
    }
    return userService.selectUserById(user.getUserId());
  }

  /** 删除用户 */
  @PreAuthorize("hasAnyAuthority('system:user:remove')")
  // @Log(title = "用户管理", businessType = BusinessType.DELETE)
  @DeleteMapping("/{userIds}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void remove(@PathVariable Long[] userIds, @AuthenticationPrincipal LoginUser loginUser) {
    if (ArrayUtils.contains(userIds, loginUser.getUserId())) {
      throw new BadRequestException("当前用户不能删除");
    }
    userService.deleteUserByIds(userIds);
  }

  /** 重置密码 */
  @PreAuthorize("hasAnyAuthority('system:user:resetPwd')")
  // @Log(title = "用户管理", businessType = BusinessType.UPDATE)
  @PutMapping("/resetPwd")
  public SysUserDTO resetPwd(
      @RequestBody SysUserDTO user, @AuthenticationPrincipal LoginUser loginUser) {
    userService.checkUserAllowed(user);
    userService.checkUserDataScope(user.getUserId(), loginUser.getUserId());
    userService.resetPwd(user);
    return userService.selectUserById(user.getUserId());
  }

  /** 状态修改 */
  @PreAuthorize("hasAnyAuthority('system:user:edit')")
  // @Log(title = "用户管理", businessType = BusinessType.UPDATE)
  @PutMapping("/changeStatus")
  public void changeStatus(
      @RequestBody SysUserDTO user, @AuthenticationPrincipal LoginUser loginUser) {
    userService.checkUserAllowed(user);
    userService.checkUserDataScope(user.getUserId(), loginUser.getUserId());
    userService.updateUserStatus(user);
  }

  /** 根据用户编号获取授权角色 */
  @PreAuthorize("hasAnyAuthority('system:user:query')")
  @GetMapping("/authRole/{userId}")
  public SysUserDTO authRole(@PathVariable Long userId) {
    return userService.selectUserById(userId);
  }

  /** 用户授权角色 */
  @PreAuthorize("hasAnyAuthority('system:user:edit')")
  // @Log(title = "用户管理", businessType = BusinessType.GRANT)
  @PutMapping("/authRole")
  @ResponseStatus(HttpStatus.CREATED)
  public void insertAuthRole(Long userId, Long[] roleIds) {
    userService.checkUserDataScope(userId, SecurityUtils.getUserId());
    for (Long roleId : roleIds) {
      roleService.checkRoleDataScope(SecurityUtils.getUserId(), roleId);
    }
    userService.insertUserAuth(userId, roleIds);
  }

  /** 获取部门树列表 */
  @PreAuthorize("hasAnyAuthority('system:user:list')")
  @GetMapping("/deptTree")
  public List<TreeSelect> deptTree(SysDeptQuery query) {
    return deptService.buildDeptTreeSelect(deptService.selectDeptTreeList(query));
  }
}
