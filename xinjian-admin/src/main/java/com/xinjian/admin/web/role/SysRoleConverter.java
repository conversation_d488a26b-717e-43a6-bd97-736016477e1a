package com.xinjian.admin.web.role;

import com.xinjian.admin.web.role.entity.SysRole;
import com.xinjian.common.core.domain.dto.SysRoleDTO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SysRoleConverter {

  @Mapping(target = "permissions", ignore = true)
  SysRoleDTO toDto(SysRole entity);

  @Mapping(target = "roleId", ignore = true)
  @Mapping(target = "isDeleted", ignore = true)
  @Mapping(target = "remark", ignore = true)
  @Mapping(target = "searchValue", ignore = true)
  @Mapping(target = "menuIds", ignore = true)
  @Mapping(target = "deptIds", ignore = true)
  @Mapping(target = "permissions", ignore = true)
  SysRole toEntity(SysRoleDTO dto);

  @Mapping(target = "isDeleted", ignore = true)
  @Mapping(target = "remark", ignore = true)
  @Mapping(target = "searchValue", ignore = true)
  @Mapping(target = "menuIds", ignore = true)
  @Mapping(target = "deptIds", ignore = true)
  @Mapping(target = "permissions", ignore = true)
  void updateEntity(SysRoleDTO dto, @MappingTarget SysRole entity);

  List<SysRoleDTO> toDtoList(List<SysRole> entities);

  List<SysRole> toEntityList(List<SysRoleDTO> dtos);
}
