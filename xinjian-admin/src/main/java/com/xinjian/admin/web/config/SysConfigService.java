package com.xinjian.admin.web.config;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.config.mapper.SysConfigMapper;
import com.xinjian.common.constant.CacheConstants;
import com.xinjian.common.constant.SystemConstants;
import com.xinjian.common.core.domain.entity.SysConfig;
import com.xinjian.common.core.domain.query.SysConfigQuery;
import com.xinjian.common.core.text.Convert;
import com.xinjian.common.exception.status400.BadRequestException;
import com.xinjian.common.properties.PaginationProperties;
import com.xinjian.common.service.ISysConfigService;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/** 参数配置 服务层实现 */
@Service
@RequiredArgsConstructor
public class SysConfigService implements ISysConfigService {

  private final SysConfigMapper configMapper;
  private final PaginationProperties paginationProperties;

  @Autowired @Lazy private SysConfigService self;

  /**
   * 查询参数配置信息
   *
   * @param configId 参数配置 ID
   * @return 参数配置信息
   */
  public SysConfig selectConfigById(Long configId) {
    SysConfig config = new SysConfig();
    config.setConfigId(configId);
    return configMapper.selectConfig(config);
  }

  /**
   * 根据键名查询参数配置信息
   *
   * @param configKey 参数 key
   * @return 参数键值
   */
  @Cacheable(cacheNames = CacheConstants.SYS_CONFIG_KEY, key = "#configKey")
  public String selectConfigByKey(String configKey) {
    SysConfig config = new SysConfig();
    config.setConfigKey(configKey);
    SysConfig retConfig = configMapper.selectConfig(config);
    if (Objects.nonNull(retConfig)) {
      return retConfig.getConfigValue();
    }
    return StringUtils.EMPTY;
  }

  /**
   * 获取验证码开关
   *
   * @return true 开启，false 关闭
   */
  public boolean selectCaptchaEnabled() {
    String captchaEnabled = selectConfigByKey("sys.account.captchaEnabled");
    if (StringUtils.isBlank(captchaEnabled)) {
      return true;
    }
    return Convert.toBool(captchaEnabled);
  }

  /**
   * 查询参数配置列表
   *
   * @param query 查询参数对象
   * @return 参数配置集合
   */
  public List<SysConfig> selectConfigList(SysConfigQuery query) {
    return configMapper.selectConfigList(query);
  }

  /**
   * 分页查询参数配置集合
   *
   * @param query 查询参数对象
   * @return 分页参数配置集合
   */
  public IPage<SysConfig> selectConfigListByPage(SysConfigQuery query) {

    IPage<SysConfig> page = query.buildPage(paginationProperties);
    return configMapper.selectConfigListByPage(page, query);
  }

  /**
   * 新增参数配置
   *
   * @param config 参数配置信息
   * @return 结果
   */
  @CacheEvict(cacheNames = CacheConstants.SYS_CONFIG_KEY, key = "#config.configKey")
  public int insertConfig(SysConfig config) {
    return configMapper.insertConfig(config);
  }

  /**
   * 修改参数配置
   *
   * @param config 参数配置信息
   * @return 结果
   */
  @Transactional
  @CacheEvict(cacheNames = CacheConstants.SYS_CONFIG_KEY, key = "#config.configKey")
  public int updateConfig(SysConfig config) {
    SysConfig oldConfig = configMapper.selectConfigById(config.getConfigId());
    if (!Objects.equals(oldConfig.getConfigKey(), config.getConfigKey())) {
      // 当 Key 变化时，必须同时清除旧 Key 的缓存
      self.clearCache(oldConfig.getConfigKey());
    }
    return configMapper.updateConfig(config);
  }

  /**
   * 批量删除参数信息
   *
   * @param configIds 需要删除的参数 ID
   */
  public void deleteConfigByIds(Long[] configIds) {
    for (Long configId : configIds) {
      SysConfig config = selectConfigById(configId);
      if (StringUtils.equals(SystemConstants.YES, config.getConfigType())) {
        throw new BadRequestException(String.format("内置参数 [%s] 不能删除", config.getConfigKey()));
      }
      configMapper.deleteConfigById(configId);
      // 调用带注解的方法清除缓存
      clearCache(config.getConfigKey());
    }
  }

  /** 加载参数缓存数据 */
  public void loadingConfigCache() {
    configMapper
        .selectConfigList(new SysConfigQuery())
        .forEach(config -> self.selectConfigByKey(config.getConfigKey()));
  }

  /** 清空参数缓存数据 */
  @CacheEvict(cacheNames = CacheConstants.SYS_CONFIG_KEY, allEntries = true)
  public void clearAllConfigCache() {
    // 不再使用 .keys() 命令
  }

  /** 重置参数缓存数据 */
  public void resetConfigCache() {
    clearAllConfigCache();
    loadingConfigCache();
  }

  /**
   * 校验参数键名是否唯一
   *
   * @param config 参数配置信息
   * @return 结果
   */
  public boolean checkConfigKeyUnique(SysConfig config) {
    Long configId = Objects.isNull(config.getConfigId()) ? -1L : config.getConfigId();
    SysConfig info = configMapper.checkConfigKeyUnique(config.getConfigKey());
    if (Objects.nonNull(info) && info.getConfigId().longValue() != configId.longValue()) {
      return false;
    }
    return true;
  }

  /**
   * 清除指定配置的缓存
   *
   * @param configKey 参数键
   */
  @CacheEvict(cacheNames = CacheConstants.SYS_CONFIG_KEY, key = "#configKey")
  public void clearCache(String configKey) {
    // 专用清除方法，通过注解清除缓存
  }
}
