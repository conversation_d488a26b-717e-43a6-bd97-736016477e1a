package com.xinjian.admin.web.config.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.common.core.domain.entity.SysConfig;
import com.xinjian.common.core.domain.query.SysConfigQuery;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/** 参数配置 数据层 */
public interface SysConfigMapper extends BaseMapper<SysConfig> {
  /**
   * 查询参数配置信息
   *
   * @param config 参数配置信息
   * @return 参数配置信息
   */
  public SysConfig selectConfig(SysConfig config);

  /**
   * 通过 ID 查询配置
   *
   * @param configId 参数 ID
   * @return 参数配置信息
   */
  public SysConfig selectConfigById(Long configId);

  /**
   * 查询参数配置列表
   *
   * @param query 查询参数对象
   * @return 参数配置集合
   */
  public List<SysConfig> selectConfigList(SysConfigQuery query);

  /**
   * 查询参数配置集合
   *
   * @param page 分页对象
   * @param query 查询参数对象
   * @return 参数配置分页集合信息
   */
  public IPage<SysConfig> selectConfigListByPage(
      @Param("page") IPage<SysConfig> page, @Param("query") SysConfigQuery query);

  /**
   * 根据键名查询参数配置信息
   *
   * @param configKey 参数键名
   * @return 参数配置信息
   */
  public SysConfig checkConfigKeyUnique(String configKey);

  /**
   * 新增参数配置
   *
   * @param config 参数配置信息
   * @return 结果
   */
  public int insertConfig(SysConfig config);

  /**
   * 修改参数配置
   *
   * @param config 参数配置信息
   * @return 结果
   */
  public int updateConfig(SysConfig config);

  /**
   * 删除参数配置
   *
   * @param configId 参数 ID
   * @return 结果
   */
  public int deleteConfigById(Long configId);

  /**
   * 批量删除参数信息
   *
   * @param configIds 需要删除的参数 ID
   * @return 结果
   */
  public int deleteConfigByIds(Long[] configIds);
}
