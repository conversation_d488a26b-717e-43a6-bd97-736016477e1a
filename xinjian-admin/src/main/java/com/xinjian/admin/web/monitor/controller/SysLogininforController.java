package com.xinjian.admin.web.monitor.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.monitor.service.SysLogininforService;
import com.xinjian.common.core.domain.entity.SysLogininfor;
import com.xinjian.common.core.domain.query.SysLogininforQuery;
import com.xinjian.common.core.page.TableDataInfo;
import com.xinjian.common.utils.poi.ExcelUtil;
import com.xinjian.framework.web.service.PasswordService;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/** 系统访问记录 */
@RestController
@RequestMapping("/monitor/logininfor")
public class SysLogininforController {
  @Autowired private SysLogininforService logininforService;

  @Autowired private PasswordService passwordService;

  @PreAuthorize("hasAnyAuthority('monitor:logininfor:list')")
  @GetMapping("/list")
  public TableDataInfo<SysLogininfor> list(SysLogininforQuery query) {
    IPage<SysLogininfor> page = logininforService.selectLogininforListByPage(query);
    return new TableDataInfo<>(page);
  }

  // @Log(title = "登录日志", businessType = BusinessType.EXPORT)
  @PreAuthorize("hasAnyAuthority('monitor:logininfor:export')")
  @PostMapping("/export")
  public void export(HttpServletResponse response, SysLogininforQuery query) {
    List<SysLogininfor> list = logininforService.selectLogininforList(query);
    ExcelUtil<SysLogininfor> util = new ExcelUtil<SysLogininfor>(SysLogininfor.class);
    util.exportExcel(response, list, "登录日志");
  }

  @PreAuthorize("hasAnyAuthority('monitor:logininfor:remove')")
  // @Log(title = "登录日志", businessType = BusinessType.DELETE)
  @DeleteMapping("/{infoIds}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void remove(@PathVariable Long[] infoIds) {
    logininforService.deleteLogininforByIds(infoIds);
  }

  @PreAuthorize("hasAnyAuthority('monitor:logininfor:remove')")
  // @Log(title = "登录日志", businessType = BusinessType.CLEAN)
  @DeleteMapping("/clean")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void clean() {
    logininforService.cleanLogininfor();
  }

  @PreAuthorize("hasAnyAuthority('monitor:logininfor:unlock')")
  // @Log(title = "账户解锁", businessType = BusinessType.OTHER)
  @GetMapping("/unlock/{userName}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void unlock(@PathVariable("userName") String userName) {
    passwordService.clearPasswordErrorCount(userName);
  }
}
