package com.xinjian.admin.web.post;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.entity.SysPost;
import com.xinjian.common.core.domain.query.SysPostQuery;
import com.xinjian.common.core.page.TableDataInfo;
import com.xinjian.common.exception.status409.ConflictException;
import com.xinjian.common.utils.poi.ExcelUtil;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/** 岗位信息操作处理 */
@RestController
@RequestMapping("/system/post")
public class SysPostController {
  @Autowired private SysPostService postService;

  /** 获取岗位列表 */
  @PreAuthorize("hasAnyAuthority('system:post:list')")
  @GetMapping("/list")
  public TableDataInfo<SysPost> list(SysPostQuery query) {
    IPage<SysPost> page = postService.selectPostListByPage(query);
    return new TableDataInfo<>(page);
  }

  // @Log(title = "岗位管理", businessType = BusinessType.EXPORT)
  @PreAuthorize("hasAnyAuthority('system:post:export')")
  @PostMapping("/export")
  public void export(HttpServletResponse response, SysPost post) {
    List<SysPost> list = postService.selectPostList(post);
    ExcelUtil<SysPost> util = new ExcelUtil<SysPost>(SysPost.class);
    util.exportExcel(response, list, "岗位数据");
  }

  /** 根据岗位编号获取详细信息 */
  @PreAuthorize("hasAnyAuthority('system:post:query')")
  @GetMapping(value = "/{postId}")
  public SysPost getInfo(@PathVariable Long postId) {
    return postService.selectPostById(postId);
  }

  /** 新增岗位 */
  @PreAuthorize("hasAnyAuthority('system:post:add')")
  // @Log(title = "岗位管理", businessType = BusinessType.INSERT)
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public SysPost add(
      @Validated @RequestBody SysPost post, @AuthenticationPrincipal LoginUser loginUser) {
    if (!postService.checkPostNameUnique(post)) {
      throw new ConflictException("新增岗位'" + post.getPostName() + "'失败，岗位名称已存在");
    }
    if (!postService.checkPostCodeUnique(post)) {
      throw new ConflictException("新增岗位'" + post.getPostName() + "'失败，岗位编码已存在");
    }
    postService.insertPost(post);
    return post;
  }

  /** 修改岗位 */
  @PreAuthorize("hasAnyAuthority('system:post:edit')")
  // @Log(title = "岗位管理", businessType = BusinessType.UPDATE)
  @PutMapping
  public SysPost edit(
      @Validated @RequestBody SysPost post, @AuthenticationPrincipal LoginUser loginUser) {
    if (!postService.checkPostNameUnique(post)) {
      throw new ConflictException("修改岗位'" + post.getPostName() + "'失败，岗位名称已存在");
    }
    if (!postService.checkPostCodeUnique(post)) {
      throw new ConflictException("修改岗位'" + post.getPostName() + "'失败，岗位编码已存在");
    }
    postService.updatePost(post);
    return post;
  }

  /** 删除岗位 */
  @PreAuthorize("hasAnyAuthority('system:post:remove')")
  // @Log(title = "岗位管理", businessType = BusinessType.DELETE)
  @DeleteMapping("/{postIds}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void remove(@PathVariable Long[] postIds) {
    postService.deletePostByIds(postIds);
  }

  /** 获取岗位选择框列表 */
  @GetMapping("/optionselect")
  public List<SysPost> optionselect() {
    return postService.selectPostAll();
  }
}
