package com.xinjian.admin.web.captcha;

import com.xinjian.module.captcha.domain.CaptchaGenerationResult;
import com.xinjian.module.captcha.service.CaptchaService;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/** 验证码演示控制器 展示如何使用新的验证码服务 */
@RestController
@RequestMapping("/api/demo")
@RequiredArgsConstructor
public class CaptchaDemoController {

  private final CaptchaService captchaService;

  /** 生成验证码 */
  @GetMapping("/captcha")
  public Map<String, Object> generateCaptcha() {
    CaptchaGenerationResult result = captchaService.generate();

    Map<String, Object> response = new HashMap<>();
    response.put("success", true);
    response.put("data", result);

    return response;
  }

  /** 验证验证码 */
  @PostMapping("/validate")
  public Map<String, Object> validateCaptcha(@RequestBody Map<String, String> request) {
    String key = request.get("key");
    String code = request.get("code");

    Map<String, Object> response = new HashMap<>();

    try {
      captchaService.validate(key, code);
      response.put("success", true);
      response.put("message", "验证码正确");
    } catch (Exception e) {
      response.put("success", false);
      response.put("message", e.getMessage());
    }

    return response;
  }

  /** 模拟登录接口 */
  @PostMapping("/login")
  public Map<String, Object> login(@RequestBody Map<String, String> request) {
    String username = request.get("username");
    String password = request.get("password");
    String captchaKey = request.get("captchaKey");
    String captchaCode = request.get("captchaCode");

    Map<String, Object> response = new HashMap<>();

    try {
      // 1. 首先验证验证码
      captchaService.validate(captchaKey, captchaCode);

      // 2. 验证用户名和密码（这里只是示例）
      if ("admin".equals(username) && "123456".equals(password)) {
        response.put("success", true);
        response.put("message", "登录成功");
        response.put("token", "mock-jwt-token");
      } else {
        response.put("success", false);
        response.put("message", "用户名或密码错误");
      }
    } catch (Exception e) {
      response.put("success", false);
      response.put("message", e.getMessage());
    }

    return response;
  }
}
