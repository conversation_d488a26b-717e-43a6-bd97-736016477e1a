package com.xinjian.admin.web.user.dto;

import javax.validation.constraints.Email;
import javax.validation.constraints.Size;

/** 用户信息更新请求 DTO */
public class SysUserUpdateDTO {

  /** 用户昵称 */
  @Size(min = 0, max = 30, message = "用户昵称长度不能超过 30 个字符")
  private String nickName;

  /** 用户邮箱 */
  @Email(message = "邮箱格式不正确")
  @Size(min = 0, max = 254, message = "邮箱长度不能超过 254 个字符")
  private String email;

  /** 手机号码 */
  @Size(min = 0, max = 11, message = "手机号码长度不能超过 11 个字符")
  private String mobile;

  /** 用户性别（1 男 2 女 3 未知） */
  private Integer sex;

  public SysUserUpdateDTO() {}

  public String getNickName() {
    return nickName;
  }

  public void setNickName(String nickName) {
    this.nickName = nickName;
  }

  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public String getMobile() {
    return mobile;
  }

  public void setMobile(String mobile) {
    this.mobile = mobile;
  }

  public Integer getSex() {
    return sex;
  }

  public void setSex(Integer sex) {
    this.sex = sex;
  }
}
