package com.xinjian.admin.web.user;

import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.dto.SysUserDTO;
import com.xinjian.common.exception.status400.InvalidInputDataException;
import com.xinjian.common.exception.status403.ForbiddenException;
import com.xinjian.common.exception.status422.ValidationFailedException;
import com.xinjian.common.service.ISysConfigService;
import com.xinjian.common.service.ISysUserService;
import com.xinjian.common.utils.TimeUtils;
import com.xinjian.common.utils.ip.IpUtils;
import com.xinjian.framework.security.context.AuthenticationContextHolder;
import com.xinjian.framework.web.service.LoginLogAsyncService;
import com.xinjian.framework.web.service.PasswordService;
import com.xinjian.framework.web.service.TokenService;
import com.xinjian.module.captcha.service.CaptchaService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;
import org.zalando.problem.AbstractThrowableProblem;

/**
 * 登录服务用户认证
 *
 * <p>验证码校验、登录前置检查、Spring Security 认证、认证成功/失败处理、Token 生成等流程
 */
@Component
@RequiredArgsConstructor
public class SysLoginService {

  private static final Logger log = LoggerFactory.getLogger(SysLoginService.class);

  private final TokenService tokenService;
  private final AuthenticationManager authenticationManager;
  private final ISysUserService userService;
  private final PasswordService passwordService;
  private final CaptchaService captchaService;
  private final ISysConfigService configService;
  private final LoginLogAsyncService loginLogAsyncService;

  /**
   * 登录认证流程
   *
   * @param username 用户名
   * @param password 密码
   * @param code 验证码
   * @param uuid 验证码的唯一标识
   * @return 生成的访问 Token
   * @throws InvalidInputDataException 当输入数据无效时
   * @throws ValidationFailedException 当验证失败时
   * @throws ForbiddenException 当访问被拒绝时
   */
  public String login(
      final String username, final String password, final String code, final String uuid) {
    // 1. 校验验证码
    captchaService.validate(uuid, code);
    // 2. 登录前置校验
    loginPreCheck(username, password);
    // 3. 执行用户身份认证
    final Authentication authentication = performAuthentication(username, password);
    final LoginUser loginUser = (LoginUser) authentication.getPrincipal();
    // 4. 处理认证成功
    handleAuthenticationSuccess(loginUser);
    // 5. 生成并返回 Token
    return tokenService.createToken(loginUser);
  }

  /**
   * 执行核心的用户身份认证
   *
   * @param username 用户名
   * @param password 密码
   * @return 认证成功的 Authentication 对象
   */
  private Authentication performAuthentication(final String username, final String password) {
    validateAuthenticationInput(username, password);
    try {
      return executeAuthentication(username, password);
    } catch (AuthenticationException e) {
      handleAuthenticationFailure(username, e);
      return null;
    }
  }

  /**
   * 验证认证输入并设置上下文
   *
   * @param username 用户名
   * @param password 密码
   */
  private void validateAuthenticationInput(final String username, final String password) {
    final UsernamePasswordAuthenticationToken authToken =
        new UsernamePasswordAuthenticationToken(username, password);
    AuthenticationContextHolder.setContext(authToken);
  }

  /**
   * 执行 Spring Security 认证
   *
   * @param username 用户名
   * @param password 密码
   * @return 认证成功的 Authentication 对象
   */
  private Authentication executeAuthentication(final String username, final String password) {
    try {
      final UsernamePasswordAuthenticationToken authToken =
          new UsernamePasswordAuthenticationToken(username, password);
      return authenticationManager.authenticate(authToken);
    } finally {
      AuthenticationContextHolder.clearContext();
    }
  }

  /**
   * 处理认证成功的逻辑
   *
   * @param loginUser 认证成功的用户信息
   */
  private void handleAuthenticationSuccess(final LoginUser loginUser) {
    final String username = loginUser.getUsername();
    // 清除密码错误计数
    passwordService.clearPasswordErrorCount(username);
    // 记录成功登录日志
    loginLogAsyncService.recordLogininfor(username, "Success", "用户登录成功");
    // 记录用户登录信息
    recordLoginInfo(loginUser.getUserId());
  }

  private void handleAuthenticationFailure(final String username, final AuthenticationException e) {
    if (e instanceof BadCredentialsException) {
      handleBadCredentialsException(username);
    } else if (e instanceof InternalAuthenticationServiceException) {
      handleInternalAuthenticationServiceException(username, e);
    } else {
      handleUnknownAuthenticationException(username, e);
    }
  }

  /**
   * 处理用户名或密码错误异常
   *
   * @param username 尝试登录的用户名
   */
  private void handleBadCredentialsException(final String username) {
    final String errorMessage = "用户名或密码错误";
    passwordService.incrementErrorCountAndCheckLock(username);
    loginLogAsyncService.recordLogininfor(username, "Error", errorMessage);
    throw new ValidationFailedException(errorMessage);
  }

  /**
   * 处理内部认证服务异常
   *
   * @param username 尝试登录的用户名
   * @param e 认证异常
   */
  private void handleInternalAuthenticationServiceException(
      final String username, final AuthenticationException e) {
    final Throwable cause = e.getCause();
    if (cause instanceof AbstractThrowableProblem) {
      final AbstractThrowableProblem businessException = (AbstractThrowableProblem) cause;
      final String errorMessage = businessException.getDetail();
      log.info("登录校验失败：{}", errorMessage);
      loginLogAsyncService.recordLogininfor(username, "Error", errorMessage);
      throw businessException;
    }

    log.error("用户'{}'认证时发生未知内部服务异常", username, e);
    final String errorMessage = "认证服务内部错误，请联系管理员";
    loginLogAsyncService.recordLogininfor(username, "Error", e.getMessage());
    throw new InternalAuthenticationServiceException(errorMessage, e);
  }

  /**
   * 处理未知认证异常
   *
   * @param username 尝试登录的用户名
   * @param e 认证异常
   */
  private void handleUnknownAuthenticationException(
      final String username, final AuthenticationException e) {
    log.error("用户'{}'认证时发生未知异常", username, e);
    final String errorMessage = "登录时发生未知错误，请联系管理员";
    loginLogAsyncService.recordLogininfor(username, "Error", e.getMessage());
    throw new ValidationFailedException(errorMessage);
  }

  /**
   * 执行登录前的多项前置校验
   *
   * @param username 用户名
   * @param password 密码
   * @throws InvalidInputDataException 如果用户名或密码为空
   * @throws ForbiddenException 如果当前 IP 地址在黑名单中
   */
  public void loginPreCheck(final String username, final String password) {
    // 1. 检查账户是否因重试次数过多而被锁定
    passwordService.validateRetryLimit(username);

    // 2. 校验用户名和密码是否为空
    if (StringUtils.isAnyBlank(username, password)) {
      loginLogAsyncService.recordLogininfor(username, "Error", "用户名或密码不能为空");
      throw new InvalidInputDataException("用户名或密码不能为空");
    }

    // 3. 校验当前请求的 IP 是否在黑名单中
    final String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
    if (StringUtils.isNotEmpty(blackStr) && blackStr.contains(IpUtils.getIpAddr())) {
      loginLogAsyncService.recordLogininfor(username, "Error", "该 IP 地址已被禁止访问");
      throw new ForbiddenException("该 IP 地址已被禁止访问");
    }
  }

  /**
   * 记录用户的登录信息
   *
   * @param userId 用户 ID
   */
  public void recordLoginInfo(final Long userId) {
    final SysUserDTO user = new SysUserDTO();
    user.setUserId(userId);
    user.setLoginIp(IpUtils.getIpAddr());
    user.setLoginTime(TimeUtils.now());
    userService.updateUserProfile(user);
  }
}
