package com.xinjian.admin.web.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.user.entity.SysUser;
import com.xinjian.common.core.domain.query.SysUserQuery;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/** 用户表 数据层 */
public interface SysUserMapper extends BaseMapper<SysUser> {
  /**
   * 根据条件分页查询用户列表
   *
   * @param query 查询参数对象
   * @return 用户信息集合信息
   */
  public List<SysUser> selectUserList(SysUserQuery query);

  /**
   * 根据条件分页查询用户列表
   *
   * @param page 分页对象
   * @param query 查询参数对象
   * @return 当页的数据列表。分页的总数等信息，由分页插件填充到传入的 page 对象中。
   */
  public List<SysUser> selectUserListPage(
      @Param("page") IPage<SysUser> page, @Param("query") SysUserQuery query);

  /**
   * 根据条件分页查询已配用户角色列表
   *
   * @param user 用户信息
   * @return 用户信息集合信息
   */
  public List<SysUser> selectAllocatedList(SysUser user);

  /**
   * 根据条件分页查询已配用户角色列表
   *
   * @param page 分页对象
   * @param query 查询参数对象
   * @return 当页的数据列表。分页的总数等信息，由分页插件填充到传入的 page 对象中。
   */
  public List<SysUser> selectAllocatedListPage(
      @Param("page") IPage<SysUser> page, @Param("query") SysUserQuery query);

  /**
   * 根据条件分页查询未分配用户角色列表
   *
   * @param user 用户信息
   * @return 用户信息集合信息
   */
  public List<SysUser> selectUnallocatedList(SysUser user);

  /**
   * 根据条件分页查询未分配用户角色列表
   *
   * @param page 分页对象
   * @param query 查询参数对象
   * @return 当页的数据列表。分页的总数等信息，由分页插件填充到传入的 page 对象中。
   */
  public List<SysUser> selectUnallocatedListPage(
      @Param("page") IPage<SysUser> page, @Param("query") SysUserQuery query);

  /**
   * 通过用户名查询用户
   *
   * @param userName 用户名
   * @return 用户对象信息
   */
  public SysUser selectUserByUserName(String userName);

  /**
   * 通过用户 ID 查询用户
   *
   * @param userId 用户 ID
   * @return 用户对象信息
   */
  public SysUser selectUserById(Long userId);

  /**
   * 新增用户信息
   *
   * @param user 用户信息
   * @return 结果
   */
  public int insertUser(SysUser user);

  /**
   * 修改用户信息
   *
   * @param user 用户信息
   * @return 结果
   */
  public int updateUser(SysUser user);

  /**
   * 修改用户头像
   *
   * @param userName 用户名
   * @param avatar 头像地址
   * @return 结果
   */
  public int updateUserAvatar(@Param("userName") String userName, @Param("avatar") String avatar);

  /**
   * 重置用户密码
   *
   * @param userName 用户名
   * @param password 密码
   * @return 结果
   */
  public int resetUserPwd(@Param("userName") String userName, @Param("password") String password);

  /**
   * 通过用户 ID 删除用户
   *
   * @param userId 用户 ID
   * @return 结果
   */
  public int deleteUserById(Long userId);

  /**
   * 批量删除用户信息
   *
   * @param userIds 需要删除的用户 ID
   * @return 结果
   */
  public int deleteUserByIds(Long[] userIds);

  /**
   * 校验用户名称是否唯一
   *
   * @param userName 用户名称
   * @return 结果
   */
  public SysUser checkUserNameUnique(String userName);

  /**
   * 校验手机号码是否唯一
   *
   * @param mobile 手机号码
   * @return 结果
   */
  public SysUser checkMobileUnique(String mobile);

  /**
   * 校验 email 是否唯一
   *
   * @param email 用户邮箱
   * @return 结果
   */
  public SysUser checkEmailUnique(String email);
}
