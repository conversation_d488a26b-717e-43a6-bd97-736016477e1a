package com.xinjian.admin.web.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xinjian.common.core.domain.entity.SysUserPost;
import java.util.List;

/** 用户与岗位关联表 数据层 */
public interface SysUserPostMapper extends BaseMapper<SysUserPost> {
  /**
   * 通过用户 ID 删除用户和岗位关联
   *
   * @param userId 用户 ID
   * @return 结果
   */
  public int deleteUserPostByUserId(Long userId);

  /**
   * 通过岗位 ID 查询岗位使用数量
   *
   * @param postId 岗位 ID
   * @return 结果
   */
  public int countUserPostById(Long postId);

  /**
   * 批量删除用户和岗位关联
   *
   * @param ids 需要删除的数据 ID
   * @return 结果
   */
  public int deleteUserPost(Long[] ids);

  /**
   * 批量新增用户岗位信息
   *
   * @param userPostList 用户岗位列表
   * @return 结果
   */
  public int batchUserPost(List<SysUserPost> userPostList);
}
