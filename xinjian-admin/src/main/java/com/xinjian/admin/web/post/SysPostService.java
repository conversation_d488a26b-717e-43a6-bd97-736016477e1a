package com.xinjian.admin.web.post;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.post.mapper.SysPostMapper;
import com.xinjian.admin.web.user.mapper.SysUserPostMapper;
import com.xinjian.common.core.domain.entity.SysPost;
import com.xinjian.common.core.domain.query.SysPostQuery;
import com.xinjian.common.exception.status400.BadRequestException;
import com.xinjian.common.properties.PaginationProperties;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/** 岗位信息 服务层处理 */
@Service
@RequiredArgsConstructor
public class SysPostService {
  private final SysPostMapper postMapper;
  private final SysUserPostMapper userPostMapper;
  private final PaginationProperties paginationProperties;

  /**
   * 查询岗位信息集合
   *
   * @param post 岗位信息
   * @return 岗位信息集合
   */
  public List<SysPost> selectPostList(SysPost post) {
    return postMapper.selectPostList(post);
  }

  /**
   * 分页查询岗位信息集合
   *
   * <p>使用统一分页方案处理分页和排序逻辑
   *
   * @param query 查询条件
   * @return 岗位信息分页集合
   */
  public IPage<SysPost> selectPostListByPage(SysPostQuery query) {
    // 处理分页和排序逻辑
    IPage<SysPost> page = query.buildPage(paginationProperties);
    return postMapper.selectPostListByPage(page, query);
  }

  /**
   * 查询所有岗位
   *
   * @return 岗位列表
   */
  public List<SysPost> selectPostAll() {
    return postMapper.selectPostAll();
  }

  /**
   * 通过岗位 ID 查询岗位信息
   *
   * @param postId 岗位 ID
   * @return 角色对象信息
   */
  public SysPost selectPostById(Long postId) {
    return postMapper.selectPostById(postId);
  }

  /**
   * 根据用户 ID 获取岗位选择框列表
   *
   * @param userId 用户 ID
   * @return 选中岗位 ID 列表
   */
  public List<Long> selectPostListByUserId(Long userId) {
    return postMapper.selectPostListByUserId(userId);
  }

  /**
   * 校验岗位名称是否唯一
   *
   * @param post 岗位信息
   * @return 结果
   */
  public boolean checkPostNameUnique(SysPost post) {
    Long postId = Objects.isNull(post.getPostId()) ? -1L : post.getPostId();
    SysPost info = postMapper.checkPostNameUnique(post.getPostName());
    if (Objects.nonNull(info) && info.getPostId().longValue() != postId.longValue()) {
      return false;
    }
    return true;
  }

  /**
   * 校验岗位编码是否唯一
   *
   * @param post 岗位信息
   * @return 结果
   */
  public boolean checkPostCodeUnique(SysPost post) {
    Long postId = Objects.isNull(post.getPostId()) ? -1L : post.getPostId();
    SysPost info = postMapper.checkPostCodeUnique(post.getPostCode());
    if (Objects.nonNull(info) && info.getPostId().longValue() != postId.longValue()) {
      return false;
    }
    return true;
  }

  /**
   * 通过岗位 ID 查询岗位使用数量
   *
   * @param postId 岗位 ID
   * @return 结果
   */
  public int countUserPostById(Long postId) {
    return userPostMapper.countUserPostById(postId);
  }

  /**
   * 删除岗位信息
   *
   * @param postId 岗位 ID
   * @return 结果
   */
  public int deletePostById(Long postId) {
    return postMapper.deletePostById(postId);
  }

  /**
   * 批量删除岗位信息
   *
   * @param postIds 需要删除的岗位 ID
   * @throws BadRequestException 删除失败时抛出异常
   */
  public void deletePostByIds(Long[] postIds) {
    for (Long postId : postIds) {
      SysPost post = selectPostById(postId);
      if (countUserPostById(postId) > 0) {
        throw new BadRequestException(String.format("%s 已分配，不能删除", post.getPostName()));
      }
    }
    int rows = postMapper.deletePostByIds(postIds);
    if (rows == 0) {
      throw new BadRequestException("删除失败");
    }
  }

  /**
   * 新增保存岗位信息
   *
   * @param post 岗位信息
   * @return 结果
   */
  public int insertPost(SysPost post) {
    return postMapper.insertPost(post);
  }

  /**
   * 修改保存岗位信息
   *
   * @param post 岗位信息
   * @return 结果
   */
  public int updatePost(SysPost post) {
    return postMapper.updatePost(post);
  }
}
