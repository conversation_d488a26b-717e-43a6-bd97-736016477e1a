package com.xinjian.admin.web.dept;

import com.xinjian.admin.web.dept.mapper.SysDeptMapper;
import com.xinjian.admin.web.role.entity.SysRole;
import com.xinjian.admin.web.role.mapper.SysRoleMapper;
import com.xinjian.common.annotation.DataScope;
import com.xinjian.common.core.domain.TreeSelect;
import com.xinjian.common.core.domain.converter.SysDeptConverter;
import com.xinjian.common.core.domain.dto.SysDeptDTO;
import com.xinjian.common.core.domain.entity.SysDept;
import com.xinjian.common.core.domain.query.SysDeptQuery;
import com.xinjian.common.core.text.Convert;
import com.xinjian.common.exception.status400.BadRequestException;
import com.xinjian.common.service.ISysDeptService;
import com.xinjian.common.utils.SecurityUtils;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/** 部门管理 服务实现 */
@Service
public class SysDeptService implements ISysDeptService {
  @Autowired private SysDeptMapper deptMapper;

  @Autowired private SysRoleMapper roleMapper;

  @Autowired @Lazy private SysDeptService self;

  @Autowired private SysDeptConverter deptConverter;

  /**
   * 查询部门管理数据
   *
   * @param dept 部门信息
   * @return 部门信息集合
   */
  @DataScope(deptAlias = "d")
  public List<SysDeptDTO> selectDeptList(SysDeptQuery dept) {
    SysDept entity = new SysDept();
    // 手动复制查询条件
    if (dept.getDeptId() != null) {
      entity.setDeptId(dept.getDeptId());
    }
    if (dept.getParentId() != null) {
      entity.setParentId(dept.getParentId());
    }
    if (dept.getDeptName() != null) {
      entity.setDeptName(dept.getDeptName());
    }
    if (dept.getStatus() != null) {
      entity.setStatus(dept.getStatus());
    }
    List<SysDept> entities = deptMapper.selectDeptList(entity);
    return deptConverter.toDtoList(entities);
  }

  /**
   * 查询部门树结构信息
   *
   * @param dept 部门信息
   * @return 部门树信息集合
   */
  public List<SysDept> selectDeptTreeList(SysDeptQuery dept) {
    SysDept entity = new SysDept();
    // 手动复制查询条件
    if (dept.getDeptId() != null) {
      entity.setDeptId(dept.getDeptId());
    }
    if (dept.getParentId() != null) {
      entity.setParentId(dept.getParentId());
    }
    if (dept.getDeptName() != null) {
      entity.setDeptName(dept.getDeptName());
    }
    if (dept.getStatus() != null) {
      entity.setStatus(dept.getStatus());
    }
    List<SysDept> depts = deptMapper.selectDeptList(entity);
    return buildDeptTree(depts);
  }

  /**
   * 构建前端所需要树结构
   *
   * @param depts 部门列表
   * @return 树结构列表
   */
  public List<SysDept> buildDeptTree(List<SysDept> depts) {
    List<SysDept> returnList = new ArrayList<SysDept>();
    List<Long> tempList = depts.stream().map(SysDept::getDeptId).collect(Collectors.toList());
    for (SysDept dept : depts) {
      // 如果是顶级节点，遍历该父节点的所有子节点
      if (!tempList.contains(dept.getParentId())) {
        recursionFn(depts, dept);
        returnList.add(dept);
      }
    }
    if (returnList.isEmpty()) {
      returnList = depts;
    }
    return returnList;
  }

  /**
   * 构建前端所需要下拉树结构
   *
   * @param depts 部门列表
   * @return 下拉树结构列表
   */
  public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts) {
    List<SysDept> deptTrees = buildDeptTree(depts);
    return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
  }

  /**
   * 根据角色 ID 查询部门树信息
   *
   * @param roleId 角色 ID
   * @return 选中部门列表
   */
  public List<Long> selectDeptListByRoleId(Long roleId) {
    SysRole role = roleMapper.selectRoleById(roleId);
    return deptMapper.selectDeptListByRoleId(roleId, role.isDeptCheckStrictly());
  }

  /**
   * 根据角色 ID 查询部门树信息
   *
   * @param roleId 角色 ID
   * @param deptCheckStrictly 部门树选择是否关联显示
   * @return 部门树信息列表
   */
  @Override
  public List<Long> selectDeptListByRoleId(Long roleId, boolean deptCheckStrictly) {
    return deptMapper.selectDeptListByRoleId(roleId, deptCheckStrictly);
  }

  /**
   * 根据部门 ID 查询信息
   *
   * @param deptId 部门 ID
   * @return 部门信息
   */
  @Override
  public SysDeptDTO selectDeptById(Long deptId) {
    SysDept entity = deptMapper.selectDeptById(deptId);
    return deptConverter.toDto(entity);
  }

  /**
   * 根据 ID 查询所有子部门（正常状态）
   *
   * @param deptId 部门 ID
   * @return 子部门数
   */
  public int selectNormalChildrenDeptById(Long deptId) {
    return deptMapper.selectNormalChildrenDeptById(deptId);
  }

  /**
   * 是否存在子节点
   *
   * @param deptId 部门 ID
   * @return 结果
   */
  public boolean hasChildByDeptId(Long deptId) {
    int result = deptMapper.hasChildByDeptId(deptId);
    return result > 0;
  }

  /**
   * 查询部门是否存在用户
   *
   * @param deptId 部门 ID
   * @return 结果 true 存在 false 不存在
   */
  public boolean checkDeptExistUser(Long deptId) {
    int result = deptMapper.checkDeptExistUser(deptId);
    return result > 0;
  }

  /**
   * 查询部门是否存在用户
   *
   * @param deptId 部门 ID
   * @return 结果 true 存在 false 不存在
   */
  public boolean hasDeptUser(Long deptId) {
    return checkDeptExistUser(deptId);
  }

  /**
   * 查询部门是否存在用户
   *
   * @param deptIds 部门 ID 组
   * @return 结果 true 存在 false 不存在
   */
  public boolean hasDeptUser(Long[] deptIds) {
    if (deptIds == null || deptIds.length == 0) {
      return false;
    }
    for (Long deptId : deptIds) {
      if (checkDeptExistUser(deptId)) {
        return true;
      }
    }
    return false;
  }

  /**
   * 根据部门名称查询部门信息
   *
   * @param deptName 部门名称
   * @return 部门信息
   */
  public SysDept selectDeptByName(String deptName) {
    return deptMapper.checkDeptNameUnique(deptName, null);
  }

  /**
   * 根据用户 ID 查询部门
   *
   * @param userId 用户 ID
   * @return 部门信息
   */
  public SysDept selectDeptByUserId(Long userId) {
    // 这里需要根据实际业务逻辑实现
    return null;
  }

  /**
   * 修改子元素关系
   *
   * @param dept 被修改的部门
   * @param oldDeptId 修改前的父部门 ID
   */
  public void updateDeptChildren(SysDept dept, Long oldDeptId) {
    updateDeptChildren(dept.getDeptId(), dept.getAncestors(), String.valueOf(oldDeptId));
  }

  /**
   * 修改所在部门的父级部门状态
   *
   * @param dept 当前部门
   */
  public void updateParentDeptStatus(SysDept dept) {
    updateParentDeptStatusNormal(dept);
  }

  /**
   * 校验部门名称是否唯一
   *
   * @param dept 部门信息
   * @return 结果
   */
  public boolean checkDeptNameUnique(SysDeptDTO dept) {
    SysDept entity = deptConverter.toEntity(dept);
    Long deptId = Objects.isNull(entity.getDeptId()) ? -1L : entity.getDeptId();
    SysDept info = deptMapper.checkDeptNameUnique(entity.getDeptName(), entity.getParentId());
    if (Objects.nonNull(info) && info.getDeptId().longValue() != deptId.longValue()) {
      return false;
    }
    return true;
  }

  /**
   * 校验部门是否有数据权限
   *
   * @param deptId 部门 id
   */
  @Override
  public void checkDeptDataScope(Long deptId) {
    Long currentUserId = SecurityUtils.getLoginUser().getUserId();
    if (!SecurityUtils.isAdminUser(currentUserId) && Objects.nonNull(deptId)) {
      SysDeptQuery query = new SysDeptQuery();
      query.setDeptId(deptId);
      List<SysDeptDTO> depts = self.selectDeptList(query);
      if (depts.isEmpty()) {
        throw new BadRequestException("无权限访问部门数据");
      }
    }
  }

  /**
   * 校验部门是否有数据权限
   *
   * @param deptId 部门 id
   * @param currentUserId 当前用户 id
   */
  @Override
  public void checkDeptDataScope(Long deptId, Long currentUserId) {
    if (!SecurityUtils.isAdminUser(currentUserId) && Objects.nonNull(deptId)) {
      SysDeptQuery query = new SysDeptQuery();
      query.setDeptId(deptId);
      List<SysDeptDTO> depts = self.selectDeptList(query);
      if (depts.isEmpty()) {
        throw new BadRequestException("无权限访问部门数据");
      }
    }
  }

  /**
   * 新增保存部门信息
   *
   * @param dept 部门信息
   * @return 结果
   */
  public int insertDept(SysDeptDTO dept) {
    SysDept entity = deptConverter.toEntity(dept);
    SysDept info = deptMapper.selectDeptById(entity.getParentId());
    // 如果父节点不为正常状态，则不允许新增子节点
    if (!info.getStatus()) {
      throw new BadRequestException("部门已停用，不允许新增子节点");
    }
    entity.setAncestors(info.getAncestors() + "," + entity.getParentId());
    // 设置审计字段
    // entity.setCreateBy(SecurityUtils.getUserId());
    // entity.setCreateTime(LocalDateTime.now());
    // entity.setUpdateBy(SecurityUtils.getUserId());
    // entity.setUpdateTime(LocalDateTime.now());
    return deptMapper.insertDept(entity);
  }

  /**
   * 修改保存部门信息
   *
   * @param dept 部门信息
   * @return 结果
   */
  public int updateDept(SysDeptDTO dept) {
    SysDept oldEntity = deptMapper.selectDeptById(dept.getDeptId());
    deptConverter.updateEntity(dept, oldEntity);

    SysDept newParentDept = deptMapper.selectDeptById(oldEntity.getParentId());
    SysDept oldDept = deptMapper.selectDeptById(oldEntity.getDeptId());
    if (Objects.nonNull(newParentDept) && Objects.nonNull(oldDept)) {
      String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getDeptId();
      String oldAncestors = oldDept.getAncestors();
      oldEntity.setAncestors(newAncestors);
      updateDeptChildren(oldEntity.getDeptId(), newAncestors, oldAncestors);
    }
    int result = deptMapper.updateDept(oldEntity);
    if (oldEntity.getStatus()
        && StringUtils.isNotEmpty(oldEntity.getAncestors())
        && !"0".equals(oldEntity.getAncestors())) {
      // 如果该部门是启用状态，则启用该部门的所有上级部门
      updateParentDeptStatusNormal(oldEntity);
    }
    return result;
  }

  /**
   * 修改该部门的父级部门状态
   *
   * @param dept 当前部门
   */
  private void updateParentDeptStatusNormal(SysDept dept) {
    String ancestors = dept.getAncestors();
    Long[] deptIds = Convert.toLongArray(ancestors);
    deptMapper.updateDeptStatusNormal(deptIds);
  }

  /**
   * 修改子元素关系
   *
   * @param deptId 被修改的部门 ID
   * @param newAncestors 新的父 ID 集合
   * @param oldAncestors 旧的父 ID 集合
   */
  public void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors) {
    List<SysDept> children = deptMapper.selectChildrenDeptById(deptId);
    for (SysDept child : children) {
      child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
    }
    if (children.size() > 0) {
      deptMapper.updateDeptChildren(children);
    }
  }

  /**
   * 删除部门管理信息
   *
   * @param deptId 部门 ID
   * @return 结果
   * @throws BadRequestException 删除失败时抛出异常
   */
  public int deleteDeptById(Long deptId) {
    int rows = deptMapper.deleteDeptById(deptId);
    if (rows == 0) {
      throw new BadRequestException("删除失败");
    }
    return rows;
  }

  /** 递归列表 */
  private void recursionFn(List<SysDept> list, SysDept t) {
    // 得到子节点列表
    List<SysDept> childList = getChildList(list, t);
    t.setChildren(childList);
    for (SysDept tChild : childList) {
      if (hasChild(list, tChild)) {
        recursionFn(list, tChild);
      }
    }
  }

  /** 得到子节点列表 */
  private List<SysDept> getChildList(List<SysDept> list, SysDept t) {
    List<SysDept> tlist = new ArrayList<SysDept>();
    Iterator<SysDept> it = list.iterator();
    while (it.hasNext()) {
      SysDept n = (SysDept) it.next();
      if (Objects.nonNull(n.getParentId())
          && n.getParentId().longValue() == t.getDeptId().longValue()) {
        tlist.add(n);
      }
    }
    return tlist;
  }

  /** 判断是否有子节点 */
  private boolean hasChild(List<SysDept> list, SysDept t) {
    return getChildList(list, t).size() > 0;
  }
}
