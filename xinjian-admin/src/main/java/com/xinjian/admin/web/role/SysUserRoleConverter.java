package com.xinjian.admin.web.role;

import com.xinjian.common.core.domain.dto.SysUserRoleDTO;
import com.xinjian.common.core.domain.entity.SysUserRole;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SysUserRoleConverter {

  SysUserRoleDTO toDto(SysUserRole entity);

  SysUserRole toEntity(SysUserRoleDTO dto);

  List<SysUserRoleDTO> toDtoList(List<SysUserRole> entities);

  List<SysUserRole> toEntityList(List<SysUserRoleDTO> dtos);
}
