package com.xinjian.admin.web.monitor.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.monitor.service.SysOperLogService;
import com.xinjian.common.core.domain.entity.SysOperLog;
import com.xinjian.common.core.domain.query.SysOperLogQuery;
import com.xinjian.common.core.page.TableDataInfo;
import com.xinjian.common.utils.poi.ExcelUtil;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/** 操作日志记录 */
@RestController
@RequestMapping("/monitor/operlog")
public class SysOperlogController {
  @Autowired private SysOperLogService operLogService;

  @PreAuthorize("hasAnyAuthority('monitor:operlog:list')")
  @GetMapping("/list")
  public TableDataInfo<SysOperLog> list(SysOperLogQuery query) {
    IPage<SysOperLog> page = operLogService.selectOperLogListByPage(query);
    return new TableDataInfo<>(page);
  }

  // @Log(title = "操作日志", businessType = BusinessType.EXPORT)
  @PreAuthorize("hasAnyAuthority('monitor:operlog:export')")
  @PostMapping("/export")
  public void export(HttpServletResponse response, SysOperLogQuery query) {
    List<SysOperLog> list = operLogService.selectOperLogList(query);
    ExcelUtil<SysOperLog> util = new ExcelUtil<SysOperLog>(SysOperLog.class);
    util.exportExcel(response, list, "操作日志");
  }

  // @Log(title = "操作日志", businessType = BusinessType.DELETE)
  @PreAuthorize("hasAnyAuthority('monitor:operlog:remove')")
  @DeleteMapping("/{operIds}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void remove(@PathVariable Long[] operIds) {
    operLogService.deleteOperLogByIds(operIds);
  }

  // @Log(title = "操作日志", businessType = BusinessType.CLEAN)
  @PreAuthorize("hasAnyAuthority('monitor:operlog:remove')")
  @DeleteMapping("/clean")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void clean() {
    operLogService.cleanOperLog();
  }
}
