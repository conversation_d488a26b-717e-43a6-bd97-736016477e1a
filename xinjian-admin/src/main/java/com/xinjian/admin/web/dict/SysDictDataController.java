package com.xinjian.admin.web.dict;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.entity.SysDictData;
import com.xinjian.common.core.domain.query.SysDictDataQuery;
import com.xinjian.common.core.page.TableDataInfo;
import com.xinjian.common.exception.status404.ResourceNotFoundException;
import com.xinjian.common.utils.poi.ExcelUtil;
import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/** 数据字典信息 */
@RestController
@RequestMapping("/system/dict/data")
public class SysDictDataController {
  @Autowired private SysDictDataService dictDataService;

  @Autowired private SysDictTypeService dictTypeService;

  @PreAuthorize("hasAnyAuthority('system:dict:list')")
  @GetMapping("/list")
  public TableDataInfo<SysDictData> list(SysDictDataQuery query) {
    IPage<SysDictData> page = dictDataService.selectDictDataListByPage(query);
    return new TableDataInfo<>(page);
  }

  // @Log(title = "字典数据", businessType = BusinessType.EXPORT)
  @PreAuthorize("hasAnyAuthority('system:dict:export')")
  @PostMapping("/export")
  public void export(HttpServletResponse response, SysDictData dictData) {
    List<SysDictData> list = dictDataService.selectDictDataList(dictData);
    ExcelUtil<SysDictData> util = new ExcelUtil<SysDictData>(SysDictData.class);
    util.exportExcel(response, list, "字典数据");
  }

  /** 查询字典数据详细 */
  @PreAuthorize("hasAnyAuthority('system:dict:query')")
  @GetMapping(value = "/{dictCode}")
  public SysDictData getInfo(@PathVariable Long dictCode) {
    return dictDataService.selectDictDataById(dictCode);
  }

  /** 根据字典类型查询字典数据信息 */
  @GetMapping(value = "/type/{dictType}")
  public List<SysDictData> dictType(@PathVariable String dictType) {
    List<SysDictData> data = dictTypeService.selectDictDataByType(dictType);
    if (Objects.isNull(data)) {
      throw new ResourceNotFoundException("字典类型 '" + dictType + "' 不存在");
    }
    return data;
  }

  /** 新增字典类型 */
  @PreAuthorize("hasAnyAuthority('system:dict:add')")
  // @Log(title = "字典数据", businessType = BusinessType.INSERT)
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public void add(
      @Validated @RequestBody SysDictData dict, @AuthenticationPrincipal LoginUser loginUser) {
    dictDataService.insertDictData(dict);
  }

  /** 修改保存字典类型 */
  @PreAuthorize("hasAnyAuthority('system:dict:edit')")
  // @Log(title = "字典数据", businessType = BusinessType.UPDATE)
  @PutMapping
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void edit(
      @Validated @RequestBody SysDictData dict, @AuthenticationPrincipal LoginUser loginUser) {

    dictDataService.updateDictData(dict);
  }

  /** 删除字典类型 */
  @PreAuthorize("hasAnyAuthority('system:dict:remove')")
  // @Log(title = "字典类型", businessType = BusinessType.DELETE)
  @DeleteMapping("/{dictCodes}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void remove(@PathVariable Long[] dictCodes) {
    dictDataService.deleteDictDataByIds(dictCodes);
  }
}
