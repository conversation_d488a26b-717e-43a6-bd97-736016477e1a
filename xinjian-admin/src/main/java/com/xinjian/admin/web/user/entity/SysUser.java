package com.xinjian.admin.web.user.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.xinjian.common.annotation.Excel;
import com.xinjian.common.annotation.Excel.ColumnType;
import com.xinjian.common.annotation.Excel.Type;
import com.xinjian.common.annotation.FullPath;
import com.xinjian.common.core.domain.BaseEntity;
import com.xinjian.common.xss.Xss;
import java.time.LocalDateTime;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** 用户对象 sys_user */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUser extends BaseEntity {
  private static final long serialVersionUID = 1L;

  /** 用户 ID */
  @Excel(name = "用户序号", type = Type.EXPORT, cellType = ColumnType.NUMERIC, prompt = "用户编号")
  @TableId(type = IdType.AUTO)
  private Long userId;

  /** 部门 ID */
  @Excel(name = "部门编号", type = Type.IMPORT)
  private Long deptId;

  /** 用户账号 */
  @Excel(name = "登录名称")
  @Xss(message = "用户账号不能包含脚本字符")
  @NotBlank(message = "用户账号不能为空")
  @Size(min = 0, max = 30, message = "用户账号长度不能超过 30 个字符")
  private String userName;

  /** 用户昵称 */
  @Excel(name = "用户名称")
  @Xss(message = "用户昵称不能包含脚本字符")
  @Size(min = 0, max = 30, message = "用户昵称长度不能超过 30 个字符")
  private String nickName;

  /** 用户类型（0 系统用户 1 第三方用户 2 其他用户） */
  @Excel(name = "用户类型", readConverterExp = "0=系统用户，1=第三方用户，2=其他用户")
  private Integer userType;

  /** 用户邮箱 */
  @Excel(name = "用户邮箱")
  @Email(message = "邮箱格式不正确")
  @Size(min = 0, max = 254, message = "邮箱长度不能超过 254 个字符")
  private String email;

  /** 手机号码 */
  @Excel(name = "手机号码", cellType = ColumnType.TEXT)
  @Size(min = 0, max = 11, message = "手机号码长度不能超过 11 个字符")
  private String mobile;

  /** 用户性别（1 男 2 女 3 未知） */
  @Excel(name = "用户性别", readConverterExp = "1=男，2=女，3=未知")
  private Integer sex;

  /** 用户头像 */
  @FullPath private String avatar;

  /** 密码 */
  private String password;

  /** 帐号状态（true 正常 false 停用） */
  @Excel(name = "帐号状态", readConverterExp = "true=正常，false=停用")
  private Boolean status;

  /** 删除标志（false 未删除 true 已删除） */
  @TableLogic
  @TableField(fill = FieldFill.INSERT)
  private Boolean isDeleted;

  /** 最后登录 IP */
  @Excel(name = "最后登录 IP", type = Type.EXPORT)
  private String loginIp;

  /** 最后登录时间 */
  @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Type.EXPORT)
  private LocalDateTime loginTime;

  // 以下字段已移除，因为它们不是数据库字段，属于 API 展示层逻辑
  // private SysDept dept;
  // @JsonIgnore private List<SysRole> roles;
  // private Long[] roleIds;
  // private Long[] postIds;
  // private Long roleId;

  public SysUser() {}

  public SysUser(Long userId) {
    this.userId = userId;
  }
}
