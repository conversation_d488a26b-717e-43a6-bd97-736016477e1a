package com.xinjian.admin.web.user;

import com.xinjian.admin.web.user.dto.AvatarResponse;
import com.xinjian.admin.web.user.dto.SysUserUpdateDTO;
import com.xinjian.admin.web.user.dto.UserProfileResponse;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.dto.SysUserDTO;
import com.xinjian.common.exception.status409.ConflictException;
import com.xinjian.common.exception.status422.BusinessRuleViolationException;
import com.xinjian.common.exception.status422.ValidationFailedException;
import com.xinjian.common.exception.status500.ServiceException;
import com.xinjian.common.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/** 个人信息 业务处理 */
@Slf4j
@RestController
@RequestMapping("/system/user/profile")
public class SysProfileController {
  @Autowired private ISysUserService userService;
  @Autowired private PasswordEncoder passwordEncoder;

  /** 个人信息 */
  @GetMapping
  public UserProfileResponse profile(@AuthenticationPrincipal LoginUser loginUser) {
    SysUserDTO user = userService.selectUserByUserName(loginUser.getUsername());
    return new UserProfileResponse(
        user,
        userService.selectUserRoleGroup(loginUser.getUsername()),
        userService.selectUserPostGroup(loginUser.getUsername()));
  }

  /** 修改用户 */
  // @Log(title = "个人信息", businessType = BusinessType.UPDATE)
  @PutMapping
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void updateProfile(
      @RequestBody SysUserUpdateDTO updateUser, @AuthenticationPrincipal LoginUser loginUser) {
    SysUserDTO currentUser = userService.selectUserByUserName(loginUser.getUsername());

    // 更新用户信息
    currentUser.setNickName(updateUser.getNickName());
    currentUser.setEmail(updateUser.getEmail());
    currentUser.setMobile(updateUser.getMobile());
    currentUser.setSex(updateUser.getSex());
    if (StringUtils.isNotEmpty(updateUser.getMobile())
        && !userService.checkMobileUnique(currentUser)) {
      throw new ConflictException("修改用户'" + loginUser.getUsername() + "'失败，手机号码已存在");
    }
    if (StringUtils.isNotEmpty(updateUser.getEmail())
        && !userService.checkEmailUnique(currentUser)) {
      throw new ConflictException("修改用户'" + loginUser.getUsername() + "'失败，邮箱账号已存在");
    }
    if (userService.updateUserProfile(currentUser) <= 0) {
      throw new ServiceException("修改个人信息异常，请联系管理员");
    }
    // 个人基本信息（昵称、邮箱、手机号）的变更不影响认证授权，无需更新缓存
  }

  /** 重置密码 */
  // @Log(title = "个人信息", businessType = BusinessType.UPDATE)
  @PutMapping("/updatePwd")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void updatePwd(
      String oldPassword, String newPassword, @AuthenticationPrincipal LoginUser loginUser) {
    String userName = loginUser.getUsername();
    String password = loginUser.getPassword();
    if (!passwordEncoder.matches(oldPassword, password)) {
      throw new ValidationFailedException("修改密码失败，旧密码错误");
    }
    if (passwordEncoder.matches(newPassword, password)) {
      throw new BusinessRuleViolationException("新密码不能与旧密码相同");
    }
    newPassword = passwordEncoder.encode(newPassword);
    if (userService.resetUserPwd(userName, newPassword) > 0) {
      // 密码变更后，应该让用户重新登录以确保安全
      // 或者可以考虑创建一个密码更新事件，由监听器来处理相关的缓存和安全策略
      log.info("用户 {} 密码已更新，建议用户重新登录", userName);
    } else {
      throw new ServiceException("修改密码异常，请联系管理员");
    }
  }

  /** 头像上传 */
  // @Log(title = "用户头像", businessType = BusinessType.UPDATE)
  @PostMapping("/avatar")
  public AvatarResponse avatar(
      @RequestParam("avatarfile") MultipartFile file,
      @AuthenticationPrincipal LoginUser loginUser) {
    // 头像更新只需要持久化到数据库，无需更新缓存
    // LoginUser 是认证授权对象，头像变更不影响其权限和身份
    String avatarUrl = userService.updateUserAvatar(loginUser.getUserId(), file);
    return new AvatarResponse(avatarUrl);
  }
}
