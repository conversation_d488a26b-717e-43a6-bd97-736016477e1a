package com.xinjian.admin.web.user;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.config.SysConfigService;
import com.xinjian.admin.web.dept.SysDeptService;
import com.xinjian.admin.web.post.mapper.SysPostMapper;
import com.xinjian.admin.web.role.entity.SysRole;
import com.xinjian.admin.web.role.mapper.SysRoleMapper;
import com.xinjian.admin.web.user.entity.SysUser;
import com.xinjian.admin.web.user.mapper.SysUserMapper;
import com.xinjian.admin.web.user.mapper.SysUserPostMapper;
import com.xinjian.admin.web.user.mapper.SysUserRoleMapper;
import com.xinjian.common.annotation.DataScope;
import com.xinjian.common.core.domain.dto.SysUserDTO;
import com.xinjian.common.core.domain.dto.UserPrincipalDTO;
import com.xinjian.common.core.domain.entity.SysPost;
import com.xinjian.common.core.domain.entity.SysUserPost;
import com.xinjian.common.core.domain.entity.SysUserRole;
import com.xinjian.common.core.domain.query.SysUserQuery;
import com.xinjian.common.exception.status400.BadRequestException;
import com.xinjian.common.exception.status500.ServiceException;
import com.xinjian.common.properties.PaginationProperties;
import com.xinjian.common.service.FileStorageService;
import com.xinjian.common.service.ISysUserService;
import com.xinjian.common.utils.SecurityUtils;
import com.xinjian.common.utils.bean.BeanValidators;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

/** 用户 业务层处理 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysUserService implements ISysUserService {
  private final Validator validator;
  private final SysUserMapper userMapper;
  private final SysRoleMapper roleMapper;
  private final SysPostMapper postMapper;
  private final SysUserRoleMapper userRoleMapper;
  private final SysUserPostMapper userPostMapper;
  private final SysConfigService configService;
  private final SysDeptService deptService;
  private final PaginationProperties paginationProperties;
  private final PasswordEncoder passwordEncoder;
  private final SysUserConverter userConverter;
  private final FileStorageService fileStorageService;

  @Autowired @Lazy private SysUserService self;

  /**
   * 根据条件分页查询用户列表
   *
   * @param query 查询参数对象
   * @return 用户信息集合信息
   */
  @DataScope(deptAlias = "d", userAlias = "u")
  public List<SysUserDTO> selectUserList(SysUserQuery query) {
    List<SysUser> users = userMapper.selectUserList(query);
    return users.stream().map(userConverter::toDto).collect(java.util.stream.Collectors.toList());
  }

  /**
   * 根据条件分页查询用户列表
   *
   * @param query 查询参数对象
   * @return 分页用户信息集合信息
   */
  @DataScope(deptAlias = "d", userAlias = "u")
  public IPage<SysUserDTO> selectUserListByPage(SysUserQuery query) {

    IPage<SysUser> page = query.buildPage(paginationProperties);

    // 调用 Mapper 方法查询数据
    List<SysUser> records = userMapper.selectUserListPage(page, query);
    page.setRecords(records);

    return page.convert(userConverter::toDto);
  }

  /**
   * 根据条件分页查询已分配用户角色列表
   *
   * @param user 用户信息
   * @return 用户信息集合信息
   */
  @DataScope(deptAlias = "d", userAlias = "u")
  public List<SysUser> selectAllocatedList(SysUser user) {
    return userMapper.selectAllocatedList(user);
  }

  /**
   * 根据条件分页查询未分配用户角色列表
   *
   * @param user 用户信息
   * @return 用户信息集合信息
   */
  @DataScope(deptAlias = "d", userAlias = "u")
  public List<SysUser> selectUnallocatedList(SysUser user) {
    return userMapper.selectUnallocatedList(user);
  }

  /**
   * 根据条件分页查询已分配用户角色列表
   *
   * @param query 查询参数对象
   * @return 分页用户信息集合信息
   */
  @DataScope(deptAlias = "d", userAlias = "u")
  public IPage<SysUserDTO> selectAllocatedListByPage(SysUserQuery query) {

    IPage<SysUser> page = query.buildPage(paginationProperties);

    // 调用 Mapper 方法查询数据
    List<SysUser> records = userMapper.selectAllocatedListPage(page, query);
    page.setRecords(records);

    return page.convert(userConverter::toDto);
  }

  /**
   * 根据条件分页查询未分配用户角色列表
   *
   * @param query 查询参数对象
   * @return 分页用户信息集合信息
   */
  @DataScope(deptAlias = "d", userAlias = "u")
  public IPage<SysUserDTO> selectUnallocatedListByPage(SysUserQuery query) {

    IPage<SysUser> page = query.buildPage(paginationProperties);

    // 调用 Mapper 方法查询数据
    List<SysUser> records = userMapper.selectUnallocatedListPage(page, query);
    page.setRecords(records);

    return page.convert(userConverter::toDto);
  }

  /**
   * 通过用户名查询用户
   *
   * @param userName 用户名
   * @return 用户对象信息
   */
  public SysUserDTO selectUserByUserName(String userName) {
    SysUser user = userMapper.selectUserByUserName(userName);
    return userConverter.toDto(user);
  }

  /**
   * 通过用户名查询用户认证信息
   *
   * @param userName 用户名
   * @return 用户认证信息
   */
  @Override
  public UserPrincipalDTO selectUserPrincipalByUserName(String userName) {
    SysUser user = userMapper.selectUserByUserName(userName);
    if (user == null) {
      return null;
    }

    UserPrincipalDTO dto = new UserPrincipalDTO();
    dto.setUserId(user.getUserId());
    dto.setDeptId(user.getDeptId());
    dto.setUserName(user.getUserName());
    dto.setPassword(user.getPassword());
    dto.setStatus(user.getStatus());
    dto.setIsDeleted(user.getIsDeleted());

    return dto;
  }

  /**
   * 通过用户 ID 查询用户
   *
   * @param userId 用户 ID
   * @return 用户对象信息
   */
  public SysUserDTO selectUserById(Long userId) {
    SysUser user = userMapper.selectUserById(userId);
    SysUserDTO userDto = userConverter.toDto(user);

    return userDto;
  }

  /**
   * 通过用户 ID 查询用户认证信息
   *
   * @param userId 用户 ID
   * @return 用户认证信息
   */
  @Override
  public UserPrincipalDTO selectUserPrincipalById(Long userId) {
    SysUser user = userMapper.selectUserById(userId);
    if (user == null) {
      return null;
    }

    UserPrincipalDTO dto = new UserPrincipalDTO();
    dto.setUserId(user.getUserId());
    dto.setDeptId(user.getDeptId());
    dto.setUserName(user.getUserName());
    dto.setPassword(user.getPassword());
    dto.setStatus(user.getStatus());
    dto.setIsDeleted(user.getIsDeleted());

    return dto;
  }

  /**
   * 查询用户所属角色组
   *
   * @param userName 用户名
   * @return 结果
   */
  public String selectUserRoleGroup(String userName) {
    List<SysRole> list = roleMapper.selectRolesByUserName(userName);
    if (CollectionUtils.isEmpty(list)) {
      return StringUtils.EMPTY;
    }
    return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
  }

  /**
   * 查询用户所属岗位组
   *
   * @param userName 用户名
   * @return 结果
   */
  public String selectUserPostGroup(String userName) {
    List<SysPost> list = postMapper.selectPostsByUserName(userName);
    if (CollectionUtils.isEmpty(list)) {
      return StringUtils.EMPTY;
    }
    return list.stream().map(SysPost::getPostName).collect(Collectors.joining(","));
  }

  /**
   * 校验用户名称是否唯一
   *
   * @param user 用户信息
   * @return 结果
   */
  public boolean checkUserNameUnique(SysUserDTO user) {
    SysUser userEntity = userConverter.toEntity(user);
    Long userId = Objects.isNull(userEntity.getUserId()) ? -1L : userEntity.getUserId();
    SysUser info = userMapper.checkUserNameUnique(userEntity.getUserName());
    if (Objects.nonNull(info) && info.getUserId().longValue() != userId.longValue()) {
      return false;
    }
    return true;
  }

  /**
   * 校验手机号码是否唯一
   *
   * @param user 用户信息
   * @return
   */
  public boolean checkMobileUnique(SysUserDTO user) {
    SysUser userEntity = userConverter.toEntity(user);
    Long userId = Objects.isNull(userEntity.getUserId()) ? -1L : userEntity.getUserId();
    SysUser info = userMapper.checkMobileUnique(userEntity.getMobile());
    if (Objects.nonNull(info) && info.getUserId().longValue() != userId.longValue()) {
      return false;
    }
    return true;
  }

  /**
   * 校验 email 是否唯一
   *
   * @param user 用户信息
   * @return
   */
  public boolean checkEmailUnique(SysUserDTO user) {
    SysUser userEntity = userConverter.toEntity(user);
    Long userId = Objects.isNull(userEntity.getUserId()) ? -1L : userEntity.getUserId();
    SysUser info = userMapper.checkEmailUnique(userEntity.getEmail());
    if (Objects.nonNull(info) && info.getUserId().longValue() != userId.longValue()) {
      return false;
    }
    return true;
  }

  /**
   * 校验用户是否允许操作
   *
   * @param user 用户信息
   */
  public void checkUserAllowed(SysUserDTO user) {
    if (Objects.nonNull(user.getUserId()) && SecurityUtils.isAdminUser(user.getUserId())) {
      throw new BadRequestException("不允许操作超级管理员用户");
    }
  }

  /**
   * 校验用户是否有数据权限
   *
   * @param userId 用户 id
   * @param currentUserId 当前用户 id
   */
  public void checkUserDataScope(Long userId, Long currentUserId) {
    if (!SecurityUtils.isAdminUser(currentUserId)) {
      SysUserQuery query = new SysUserQuery();
      query.setUserId(userId);
      List<SysUserDTO> users = self.selectUserList(query);
      if (CollectionUtils.isEmpty(users)) {
        throw new BadRequestException("无权限访问用户数据");
      }
    }
  }

  /**
   * 新增保存用户信息
   *
   * @param user 用户信息
   * @return 结果
   */
  @Transactional
  public int insertUser(SysUserDTO user) {
    SysUser userEntity = userConverter.toEntity(user);
    // 新增用户信息
    int rows = userMapper.insertUser(userEntity);
    // 新增用户岗位关联
    insertUserPost(userEntity);
    // 新增用户与角色管理
    insertUserRole(userEntity);
    return rows;
  }

  /**
   * 注册用户信息
   *
   * @param user 用户信息
   * @return 结果
   */
  public boolean registerUser(SysUserDTO user) {
    SysUser userEntity = userConverter.toEntity(user);
    return userMapper.insertUser(userEntity) > 0;
  }

  /**
   * 修改保存用户信息
   *
   * @param user 用户信息
   * @return 结果
   */
  @Transactional
  public int updateUser(SysUserDTO user) {
    SysUser userEntity = userConverter.toEntity(user);
    Long userId = userEntity.getUserId();
    // 删除用户与角色关联
    userRoleMapper.deleteUserRoleByUserId(userId);
    // 新增用户与角色管理
    insertUserRole(userEntity);
    // 删除用户与岗位关联
    userPostMapper.deleteUserPostByUserId(userId);
    // 新增用户与岗位管理
    insertUserPost(userEntity);
    return userMapper.updateUser(userEntity);
  }

  /**
   * 用户授权角色
   *
   * @param userId 用户 ID
   * @param roleIds 角色组
   */
  @Transactional
  public void insertUserAuth(Long userId, Long[] roleIds) {
    userRoleMapper.deleteUserRoleByUserId(userId);
    insertUserRole(userId, roleIds);
  }

  /**
   * 修改用户状态
   *
   * @param user 用户信息
   * @return 结果
   */
  public int updateUserStatus(SysUserDTO user) {
    SysUser userEntity = userConverter.toEntity(user);
    return userMapper.updateUser(userEntity);
  }

  /**
   * 修改用户基本信息
   *
   * @param user 用户信息
   * @return 结果
   */
  public int updateUserProfile(SysUserDTO user) {
    SysUser userEntity = userConverter.toEntity(user);
    return userMapper.updateUser(userEntity);
  }

  /**
   * 修改用户头像
   *
   * @param userName 用户名
   * @param avatar 头像地址
   * @return 结果
   */
  public boolean updateUserAvatar(String userName, String avatar) {
    return userMapper.updateUserAvatar(userName, avatar) > 0;
  }

  /**
   * 重置用户密码
   *
   * @param user 用户信息
   * @return 结果
   */
  public int resetPwd(SysUserDTO user) {
    SysUser userEntity = userConverter.toEntity(user);
    return userMapper.updateUser(userEntity);
  }

  /**
   * 重置用户密码
   *
   * @param userName 用户名
   * @param password 密码
   * @return 结果
   */
  public int resetUserPwd(String userName, String password) {
    return userMapper.resetUserPwd(userName, password);
  }

  /**
   * 新增用户角色信息
   *
   * @param user 用户对象
   */
  public void insertUserRole(SysUser user) {
    // 通过查询获取用户的角色 ID 列表
    List<SysRole> roles = roleMapper.selectRolePermissionByUserId(user.getUserId());
    Long[] roleIds = roles.stream().map(SysRole::getRoleId).toArray(Long[]::new);
    this.insertUserRole(user.getUserId(), roleIds);
  }

  /**
   * 新增用户岗位信息
   *
   * @param user 用户对象
   */
  public void insertUserPost(SysUser user) {
    // 通过查询获取用户的岗位 ID 列表
    List<SysPost> posts = postMapper.selectPostsByUserId(user.getUserId());
    Long[] postIds = posts.stream().map(SysPost::getPostId).toArray(Long[]::new);

    if (postIds != null && postIds.length > 0) {
      // 新增用户与岗位管理
      List<SysUserPost> list = new ArrayList<SysUserPost>(postIds.length);
      for (Long postId : postIds) {
        SysUserPost up = new SysUserPost();
        up.setUserId(user.getUserId());
        up.setPostId(postId);
        list.add(up);
      }
      userPostMapper.batchUserPost(list);
    }
  }

  /**
   * 新增用户角色信息
   *
   * @param userId 用户 ID
   * @param roleIds 角色组
   */
  public void insertUserRole(Long userId, Long[] roleIds) {
    if (roleIds != null && roleIds.length > 0) {
      // 新增用户与角色管理
      List<SysUserRole> list = new ArrayList<SysUserRole>(roleIds.length);
      for (Long roleId : roleIds) {
        SysUserRole ur = new SysUserRole();
        ur.setUserId(userId);
        ur.setRoleId(roleId);
        list.add(ur);
      }
      userRoleMapper.batchUserRole(list);
    }
  }

  /**
   * 通过用户 ID 删除用户
   *
   * @param userId 用户 ID
   * @return 结果
   */
  @Transactional
  public int deleteUserById(Long userId) {
    // 删除用户与角色关联
    userRoleMapper.deleteUserRoleByUserId(userId);
    // 删除用户与岗位表
    userPostMapper.deleteUserPostByUserId(userId);
    return userMapper.deleteUserById(userId);
  }

  /**
   * 批量删除用户信息
   *
   * @param userIds 需要删除的用户 ID
   * @return 结果
   */
  @Transactional
  public void deleteUserByIds(Long[] userIds) {
    for (Long userId : userIds) {
      SysUserDTO userDto = new SysUserDTO();
      userDto.setUserId(userId);
      checkUserAllowed(userDto);
      checkUserDataScope(userId, SecurityUtils.getUserId());
    }
    // 删除用户与角色关联
    userRoleMapper.deleteUserRole(userIds);
    // 删除用户与岗位关联
    userPostMapper.deleteUserPost(userIds);
    int rows = userMapper.deleteUserByIds(userIds);
    if (rows == 0) {
      throw new BadRequestException("删除用户失败");
    }
  }

  /**
   * 导入用户数据
   *
   * @param userList 用户数据列表
   * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
   * @param operName 操作用户
   * @return 结果
   */
  public String importUser(List<SysUserDTO> userList, Boolean isUpdateSupport, String operName) {
    if (Objects.isNull(userList) || userList.size() == 0) {
      throw new BadRequestException("导入的用户数据不能为空");
    }
    int successNum = 0;
    int failureNum = 0;
    StringBuilder successMsg = new StringBuilder();
    StringBuilder failureMsg = new StringBuilder();
    for (SysUserDTO userDto : userList) {
      try {
        // 将 DTO 转换为 Entity
        SysUser user = userConverter.toEntity(userDto);
        // 验证是否存在这个用户
        SysUser u = userMapper.selectUserByUserName(user.getUserName());
        if (Objects.isNull(u)) {
          BeanValidators.validateWithException(validator, user);
          deptService.checkDeptDataScope(user.getDeptId(), SecurityUtils.getUserId());
          String password = configService.selectConfigByKey("sys.user.initPassword");
          user.setPassword(passwordEncoder.encode(password));
          userMapper.insertUser(user);
          successNum++;
          successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
        } else if (isUpdateSupport) {
          BeanValidators.validateWithException(validator, user);
          checkUserAllowed(userConverter.toDto(u));
          checkUserDataScope(u.getUserId(), SecurityUtils.getUserId());
          deptService.checkDeptDataScope(user.getDeptId(), SecurityUtils.getUserId());
          user.setUserId(u.getUserId());
          userMapper.updateUser(user);
          successNum++;
          successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
        } else {
          failureNum++;
          failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
        }
      } catch (Exception e) {
        failureNum++;
        String msg = "<br/>" + failureNum + "、账号 " + userDto.getUserName() + " 导入失败：";
        failureMsg.append(msg + e.getMessage());
        log.error(msg, e);
      }
    }
    if (failureNum > 0) {
      failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
      throw new BadRequestException(failureMsg.toString());
    } else {
      successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
    }
    return successMsg.toString();
  }

  @Override
  public boolean checkUserNameUnique(String userName) {
    SysUserDTO user = new SysUserDTO();
    user.setUserName(userName);
    return checkUserNameUnique(user);
  }

  @Override
  public void export(HttpServletResponse response, SysUserQuery query) {
    // TODO: 实现导出功能
  }

  @Override
  public String importData(
      org.springframework.web.multipart.MultipartFile file, boolean updateSupport, String operName)
      throws Exception {
    // TODO: 实现导入功能
    return "导入功能待实现";
  }

  @Override
  public void importTemplate(HttpServletResponse response) {
    // TODO: 实现导入模板功能
  }

  @Override
  public void insertUserAuthWithRoleIds(SysUserDTO user, Long[] roleIds) {
    insertUserAuth(user.getUserId(), roleIds);
  }

  @Override
  public List<Long> findUserIdsByRoleId(Long roleId) {
    return userRoleMapper.selectUserIdsByRoleId(roleId);
  }

  @Override
  @Transactional
  public String updateUserAvatar(Long userId, MultipartFile file) {
    try {
      if (file.isEmpty()) {
        throw new ServiceException("上传的头像文件不能为空");
      }

      // 1. 上传文件到文件存储服务，并获取返回的路径/URL
      String avatarUrl =
          fileStorageService.upload(
              fileStorageService.getAvatarPath(),
              file,
              fileStorageService.getStorageProperties().getImageExtensions());

      // 2. 将新的头像路径更新到数据库
      SysUser user = userMapper.selectUserById(userId);
      if (user == null) {
        throw new ServiceException("用户不存在");
      }

      user.setAvatar(avatarUrl);
      int result = userMapper.updateUser(user);

      if (result > 0) {
        log.info("用户 {} 头像更新成功，新头像地址：{}", userId, avatarUrl);
        return avatarUrl;
      } else {
        throw new ServiceException("更新用户头像失败");
      }
    } catch (ServiceException e) {
      throw e;
    } catch (Exception e) {
      log.error("更新用户 {} 头像时发生异常", userId, e);
      throw new ServiceException("上传头像图片异常，请联系管理员");
    }
  }
}
