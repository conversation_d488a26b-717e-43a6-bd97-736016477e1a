package com.xinjian.admin.web.dept;

import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.dto.SysDeptDTO;
import com.xinjian.common.core.domain.query.SysDeptQuery;
import com.xinjian.common.exception.status409.ConflictException;
import com.xinjian.common.exception.status422.BusinessRuleViolationException;
import com.xinjian.common.exception.status500.ServiceException;
import com.xinjian.common.service.ISysDeptService;
import java.util.List;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/** 部门信息 */
@RestController
@RequestMapping("/system/dept")
public class SysDeptController {
  @Autowired private ISysDeptService deptService;

  /** 获取部门列表 */
  @PreAuthorize("hasAnyAuthority('system:dept:list')")
  @GetMapping("/list")
  public List<SysDeptDTO> list(SysDeptQuery query) {
    return deptService.selectDeptList(query);
  }

  /** 查询部门列表（排除节点） */
  @PreAuthorize("hasAnyAuthority('system:dept:list')")
  @GetMapping("/list/exclude/{deptId}")
  public List<SysDeptDTO> excludeChild(
      @PathVariable(value = "deptId", required = false) Long deptId) {
    List<SysDeptDTO> depts = deptService.selectDeptList(new SysDeptQuery());
    depts.removeIf(
        d ->
            d.getDeptId().intValue() == deptId
                || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), deptId + ""));
    return depts;
  }

  /** 根据部门编号获取详细信息 */
  @PreAuthorize("hasAnyAuthority('system:dept:query')")
  @GetMapping(value = "/{deptId}")
  public SysDeptDTO getInfo(
      @PathVariable Long deptId, @AuthenticationPrincipal LoginUser loginUser) {
    deptService.checkDeptDataScope(deptId, loginUser.getUserId());
    return deptService.selectDeptById(deptId);
  }

  /** 新增部门 */
  @PreAuthorize("hasAnyAuthority('system:dept:add')")
  // @Log(title = "部门管理", businessType = BusinessType.INSERT)
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public SysDeptDTO add(
      @Validated @RequestBody SysDeptDTO dept, @AuthenticationPrincipal LoginUser loginUser) {
    if (!deptService.checkDeptNameUnique(dept)) {
      throw new ConflictException("新增部门'" + dept.getDeptName() + "'失败，部门名称已存在");
    }
    deptService.insertDept(dept);
    return dept;
  }

  /** 修改部门 */
  @PreAuthorize("hasAnyAuthority('system:dept:edit')")
  // @Log(title = "部门管理", businessType = BusinessType.UPDATE)
  @PutMapping
  public SysDeptDTO edit(
      @Validated @RequestBody SysDeptDTO dept, @AuthenticationPrincipal LoginUser loginUser) {
    Long deptId = dept.getDeptId();
    deptService.checkDeptDataScope(deptId, loginUser.getUserId());
    if (!deptService.checkDeptNameUnique(dept)) {
      throw new ConflictException("修改部门'" + dept.getDeptName() + "'失败，部门名称已存在");
    } else if (dept.getParentId().equals(deptId)) {
      throw new BusinessRuleViolationException("修改部门'" + dept.getDeptName() + "'失败，上级部门不能是自己");
    } else if (!dept.getStatus() && deptService.selectNormalChildrenDeptById(deptId) > 0) {
      throw new BusinessRuleViolationException("该部门包含未停用的子部门！");
    }
    if (deptService.updateDept(dept) <= 0) {
      throw new ServiceException("修改部门失败");
    }
    return dept;
  }

  /** 删除部门 */
  @PreAuthorize("hasAnyAuthority('system:dept:remove')")
  // @Log(title = "部门管理", businessType = BusinessType.DELETE)
  @DeleteMapping("/{deptId}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void remove(@PathVariable Long deptId, @AuthenticationPrincipal LoginUser loginUser) {
    if (deptService.hasChildByDeptId(deptId)) {
      throw new BusinessRuleViolationException("存在下级部门，不允许删除");
    }
    if (deptService.checkDeptExistUser(deptId)) {
      throw new BusinessRuleViolationException("部门存在用户，不允许删除");
    }
    deptService.checkDeptDataScope(deptId, loginUser.getUserId());
    deptService.deleteDeptById(deptId);
  }
}
