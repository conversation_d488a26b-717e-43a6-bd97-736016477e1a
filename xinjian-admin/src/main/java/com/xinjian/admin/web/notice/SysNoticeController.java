package com.xinjian.admin.web.notice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinjian.admin.web.notice.dto.SysNoticeDTO;
import com.xinjian.admin.web.notice.dto.SysNoticeQuery;
import com.xinjian.admin.web.notice.dto.SysNoticeRequest;
import com.xinjian.common.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/system/notice")
@RequiredArgsConstructor
public class SysNoticeController {

  private final SysNoticeService noticeService;

  @GetMapping("/list")
  public TableDataInfo<SysNoticeDTO> list(SysNoticeQuery query) {
    IPage<SysNoticeDTO> page = noticeService.selectNoticeListByPage(query);
    return new TableDataInfo<>(page);
  }

  @GetMapping("/{noticeId}")
  public SysNoticeDTO getInfo(@PathVariable Long noticeId) {
    return noticeService.selectNoticeById(noticeId);
  }

  @PostMapping
  public void add(@RequestBody SysNoticeRequest request) {
    noticeService.createNotice(request);
  }

  @PutMapping("/{noticeId}")
  public void edit(@PathVariable Long noticeId, @RequestBody SysNoticeRequest request) {
    noticeService.updateNotice(noticeId, request);
  }

  @DeleteMapping("/{noticeId}")
  public void remove(@PathVariable Long noticeId) {
    noticeService.deleteNoticeById(noticeId);
  }
}
