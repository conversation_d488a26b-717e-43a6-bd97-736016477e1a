package com.xinjian.admin.web.notice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinjian.admin.web.notice.dto.SysNoticeDTO;
import com.xinjian.admin.web.notice.dto.SysNoticeQuery;
import com.xinjian.admin.web.notice.dto.SysNoticeRequest;
import com.xinjian.admin.web.notice.entity.SysNotice;
import com.xinjian.admin.web.notice.mapper.SysNoticeMapper;
import com.xinjian.common.properties.PaginationProperties;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SysNoticeServiceImpl extends ServiceImpl<SysNoticeMapper, SysNotice>
    implements SysNoticeService {

  private final SysNoticeConverter noticeConverter;
  private final PaginationProperties paginationProperties;

  @Override
  public SysNoticeDTO selectNoticeById(Long noticeId) {
    SysNotice entity = this.getById(noticeId); // 使用继承自 ServiceImpl 的方法
    return noticeConverter.toDto(entity);
  }

  @Override
  public IPage<SysNoticeDTO> selectNoticeListByPage(SysNoticeQuery query) {
    IPage<SysNotice> page = query.buildPage(paginationProperties);
    List<SysNotice> entityList = this.baseMapper.selectNoticeListPage(page, query);
    page.setRecords(entityList);
    return page.convert(noticeConverter::toDto);
  }

  @Override
  public void createNotice(SysNoticeRequest request) {
    SysNotice entity = noticeConverter.toEntity(request);
    this.save(entity); // 使用继承自 ServiceImpl 的方法
  }

  @Override
  public void updateNotice(Long noticeId, SysNoticeRequest request) {
    SysNotice entity = noticeConverter.toEntity(request);
    entity.setNoticeId(noticeId);
    this.updateById(entity); // 使用继承自 ServiceImpl 的方法
  }

  @Override
  public void deleteNoticeById(Long noticeId) {
    this.removeById(noticeId); // 使用继承自 ServiceImpl 的方法
  }
}
