package com.xinjian.admin.init;

import com.xinjian.admin.web.config.SysConfigService;
import com.xinjian.admin.web.dict.SysDictTypeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * 缓存初始化器
 *
 * <p>通过监听 ApplicationReadyEvent，确保在应用完全就绪后，以正确的顺序预热所有缓存
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CacheInitializer implements ApplicationListener<ApplicationReadyEvent> {

  private final SysDictTypeService dictTypeService;
  private final SysConfigService configService;

  /** 当应用完全准备就绪时，此方法会被 Spring 自动调用 */
  @Override
  public void onApplicationEvent(ApplicationReadyEvent event) {
    log.info("应用已就绪，开始执行缓存预热...");
    try {
      // 预热字典缓存
      dictTypeService.loadingDictCache();
      // 预热配置缓存
      configService.loadingConfigCache();

      log.info("缓存预热任务执行完毕");
    } catch (Exception e) {
      log.error("缓存预热过程中发生严重错误", e);
    }
  }
}
