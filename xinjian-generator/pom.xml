<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>xinjian</artifactId>
    <groupId>com.xinjian</groupId>
    <version>1.0.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>xinjian-generator</artifactId>

  <description>代码生成模块</description>

  <dependencies>

    <!-- 内部模块依赖, 提供通用工具类 -->
    <dependency>
      <groupId>com.xinjian</groupId>
      <artifactId>xinjian-common</artifactId>
    </dependency>

    <!-- Velocity, 一个基于 Java 的模板引擎, 用于代码生成 -->
    <dependency>
      <groupId>org.apache.velocity</groupId>
      <artifactId>velocity-engine-core</artifactId>
    </dependency>

    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-generator</artifactId>
    </dependency>
      <dependency>
      <groupId>org.mariadb.jdbc</groupId>
      <artifactId>mariadb-java-client</artifactId>
    </dependency>

    <!-- Druid 核心库（用于 SQL 解析） -->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid</artifactId>
    </dependency>

    <!-- Lombok，通过注解简化 Java 代码 -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>

</project>
