import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;
import java.util.*;
import java.util.stream.Collectors;

/**
 * MyBatis-Plus 代码生成器
 *
 * <p>使用指南: 1. 在 TABLE_CONFIG 中配置所有需要生成的表及其前缀。 2. (可选) 在 DIRECTORY_OVERRIDE_MAP 中为特定表指定一个共享的目录名。 3.
 * 运行 main 方法，生成器会自动处理分组和独立目录。
 */
public class CodeGenerator {

  // ================================================================================================
  // =======================  在这里集中配置所有需要生成的表   ===========================================
  // ================================================================================================

  /** 基础表配置 (必须)： Key: 表名 (tableName) Value: 该表需要移除的前缀 (tablePrefix) */
  private static final Map<String, String> TABLE_CONFIG =
      new LinkedHashMap<String, String>() {
        {
          // 系统模块的表
          put("sys_user", "sys_");
          put("sys_role", "sys_");
          put("sys_menu", "sys_");
          put("sys_dept", "sys_");
          // 字典相关的表
          put("sys_dict_data", "sys_");
          put("sys_dict_type", "sys_");
        }
      };

  /** 目录覆盖配置 (可选)： Key: 表名 (tableName) Value: 希望它进入的目录名 (directoryName) */
  private static final Map<String, String> DIRECTORY_OVERRIDE_MAP =
      new HashMap<String, String>() {
        {
          put("sys_dict_data", "dict"); // <-- 指定 sys_dict_data 进入 'dict' 目录
          put("sys_dict_type", "dict"); // <-- 指定 sys_dict_type 进入 'dict' 目录
        }
      };

  public static void main(String[] args) {
    // =================================================================================================
    // =======================          通用配置（一般无需修改）         ===================================
    // =================================================================================================
    final String JDBC_URL = "******************************************************";
    final String JDBC_USERNAME = "gxxj";
    final String JDBC_PASSWORD = "Mgp^fMWTK7oRG&Q*";
    final String AUTHOR = "xinjian";
    final String PARENT_PACKAGE = "com.xinjian.admin";
    String projectPath = System.getProperty("user.dir");
    String javaOutputPath = projectPath + "/src/main/java";

    // ================================================================================================
    // =======================      1. 智能计算最终的目录分组         ====================================
    // ================================================================================================
    Map<String, List<String>> finalModuleMap = new HashMap<>();
    TABLE_CONFIG.forEach(
        (tableName, prefix) -> {
          // 优先从 OVERRIDE_MAP 中获取目录名，如果不存在，则自动计算
          String moduleName =
              DIRECTORY_OVERRIDE_MAP.getOrDefault(tableName, tableName.replace(prefix, ""));
          finalModuleMap.computeIfAbsent(moduleName, k -> new ArrayList<>()).add(tableName);
        });

    System.out.println("最终生成的模块分组如下: " + finalModuleMap);

    // ================================================================================================
    // =======================      2. 循环为每个计算出的分组生成代码      =================================
    // ================================================================================================
    finalModuleMap.forEach(
        (moduleName, tableNames) -> {
          System.out.println("================== 开始生成模块: " + moduleName + " ==================");
          System.out.println("包含的表: " + tableNames);

          // 为当前模块动态计算需要移除的表前缀集合
          Set<String> tablePrefixes =
              tableNames.stream().map(TABLE_CONFIG::get).collect(Collectors.toSet());

          String xmlOutputPath = projectPath + "/src/main/resources/mapper/" + moduleName;

          FastAutoGenerator.create(JDBC_URL, JDBC_USERNAME, JDBC_PASSWORD)
              .globalConfig(
                  builder ->
                      builder
                          .author(AUTHOR)
                          .outputDir(javaOutputPath)
                          .commentDate("yyyy-MM-dd")
                          .disableOpenDir())
              .packageConfig(
                  builder ->
                      builder
                          .parent(PARENT_PACKAGE)
                          .moduleName(moduleName)
                          .pathInfo(Collections.singletonMap(OutputFile.xml, xmlOutputPath)))
              .strategyConfig(
                  builder ->
                      builder
                          .addInclude(tableNames)
                          .addTablePrefix(tablePrefixes.toArray(new String[0])) // 动态设置前缀
                          .entityBuilder()
                          .enableLombok()
                          .enableTableFieldAnnotation()
                          .controllerBuilder()
                          .enableRestStyle()
                          .serviceBuilder()
                          .formatServiceFileName("%sService")
                          .formatServiceImplFileName("%sServiceImpl")
                          .mapperBuilder()
                          .formatMapperFileName("%sMapper")
                          .formatXmlFileName("%sMapper"))
              .templateEngine(new VelocityTemplateEngine())
              .execute();

          System.out.println(
              "================== 模块: " + moduleName + " 生成完毕! ==================\n");
        });

    System.out.println("🎉🎉🎉 所有代码已根据配置生成完毕！ 🎉🎉🎉");
  }
}
