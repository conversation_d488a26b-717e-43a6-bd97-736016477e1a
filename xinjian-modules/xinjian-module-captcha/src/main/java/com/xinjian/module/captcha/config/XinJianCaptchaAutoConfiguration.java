package com.xinjian.module.captcha.config;

import com.xinjian.module.captcha.service.CaptchaService;
import com.xinjian.module.captcha.service.CaptchaStore;
import com.xinjian.module.captcha.store.impl.InMemoryCaptchaStore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(XinJianCaptchaProperties.class)
public class XinJianCaptchaAutoConfiguration {

  @Bean
  @ConditionalOnMissingBean
  public CaptchaStore captchaStore() {
    return new InMemoryCaptchaStore();
  }

  @Bean
  @ConditionalOnProperty(
      prefix = "xinjian.captcha",
      name = "provider",
      havingValue = "kaptcha",
      matchIfMissing = true)
  public CaptchaService kaptchaCaptchaService(
      XinJianCaptchaProperties properties,
      CaptchaStore captchaStore,
      com.xinjian.module.captcha.provider.kaptcha.KaptchaConfig kaptchaConfig) {
    com.xinjian.module.captcha.provider.kaptcha.KaptchaServiceImpl service =
        new com.xinjian.module.captcha.provider.kaptcha.KaptchaServiceImpl();
    service.setProperties(properties);
    service.setCaptchaStore(captchaStore);
    service.setCaptchaProducer(kaptchaConfig.getKaptchaBean());
    service.setCaptchaProducerMath(kaptchaConfig.getKaptchaBeanMath());
    return service;
  }

  @Bean
  @ConditionalOnProperty(prefix = "xinjian.captcha", name = "provider", havingValue = "easycaptcha")
  public CaptchaService easycaptchaCaptchaService(
      XinJianCaptchaProperties properties, CaptchaStore captchaStore) {
    com.xinjian.module.captcha.provider.easycaptcha.EasyCaptchaServiceImpl service =
        new com.xinjian.module.captcha.provider.easycaptcha.EasyCaptchaServiceImpl();
    service.setProperties(properties);
    service.setCaptchaStore(captchaStore);
    return service;
  }
}
