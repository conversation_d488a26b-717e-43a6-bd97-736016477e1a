package com.xinjian.module.captcha.store.impl;

import com.xinjian.module.captcha.service.CaptchaStore;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import lombok.Getter;

public class InMemoryCaptchaStore implements CaptchaStore {
  @Getter private final Map<String, String> store = new ConcurrentHashMap<>();
  @Getter private final Map<String, Long> expireTimes = new ConcurrentHashMap<>();

  @Override
  public void save(String key, String code, long expireTimeInMinutes) {
    store.put(key, code);
    expireTimes.put(key, System.currentTimeMillis() + expireTimeInMinutes * 60 * 1000);
  }

  @Override
  public Optional<String> get(String key) {
    // 检查是否过期
    Long expireTime = expireTimes.get(key);
    if (expireTime != null && System.currentTimeMillis() > expireTime) {
      delete(key);
      return Optional.empty();
    }
    String code = store.get(key);
    return Optional.ofNullable(code);
  }

  @Override
  public void delete(String key) {
    store.remove(key);
    expireTimes.remove(key);
  }
}
