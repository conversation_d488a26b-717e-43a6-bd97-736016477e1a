<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
      </profile>
      <profile name="Annotation profile for xinjian" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/mapstruct/mapstruct-processor/1.6.3/mapstruct-processor-1.6.3.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/mapstruct/mapstruct/1.6.3/mapstruct-1.6.3.jar" />
        </processorPath>
        <module name="xinjian-framework" />
        <module name="xinjian-module-captcha" />
        <module name="xinjian-starter-redis" />
        <module name="xinjian-generator" />
        <module name="xinjian-quartz" />
        <module name="xinjian-admin" />
        <module name="xinjian-common" />
        <module name="xinjian-starter-mybatis" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="xinjian" options="-Xlint:-options" />
      <module name="xinjian-admin" options="-Xlint:-options" />
      <module name="xinjian-common" options="-Xlint:-options" />
      <module name="xinjian-framework" options="-Xlint:-options" />
      <module name="xinjian-generator" options="-Xlint:-options" />
      <module name="xinjian-module-captcha" options="-Xlint:-options" />
      <module name="xinjian-modules" options="-Xlint:-options" />
      <module name="xinjian-quartz" options="-Xlint:-options" />
      <module name="xinjian-starter-mybatis" options="-Xlint:-options" />
      <module name="xinjian-starter-redis" options="-Xlint:-options" />
      <module name="xinjian-starters" options="-Xlint:-options" />
    </option>
  </component>
</project>