<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <!-- 项目信息 -->
  <groupId>com.xinjian</groupId>
  <artifactId>xinjian</artifactId>
  <version>1.0.0</version>
  <!-- 父项目打包方式为 pom -->
  <packaging>pom</packaging>

  <name>xinjian</name>
  <description>信建管理系统</description>

  <!-- 统一属性管理 -->
  <properties>
    <!-- 项目版本 -->
    <xinjian.version>1.0.0</xinjian.version>

    <!-- Java 配置 -->
    <java.version>1.8</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

    <!-- Spring 框架 -->
    <spring-boot.version>2.7.18</spring-boot.version>
    <spring-framework.version>5.3.34</spring-framework.version>

    <!-- 数据库和数据源 -->
    <dynamic-datasource.version>4.3.1</dynamic-datasource.version>
    <mysql-connector.version>8.0.33</mysql-connector.version>
    <mariadb-connector.version>3.5.5</mariadb-connector.version>
    <oceanbase-connector.version>2.4.15</oceanbase-connector.version>

    <!-- JSON 库 -->
    <jackson.version>2.19.2</jackson.version>

    <!-- 工具库 -->
    <commons-io.version>2.20.0</commons-io.version> <!-- Apache Commons IO -->
    <commons-lang3.version>3.18.0</commons-lang3.version> <!-- Apache Commons Lang3 -->
    <guava.version>33.4.8-jre</guava.version> <!-- Google Guava -->
    <hutool.version>5.8.39</hutool.version> <!-- Hutool 工具库 -->
    <oshi.version>6.8.3</oshi.version> <!-- 系统信息 -->
    <poi.version>4.1.2</poi.version> <!-- Apache POI Excel 操作 -->
    <snakeyaml.version>2.4</snakeyaml.version> <!-- YAML 解析器 -->
    <uuid-creator.version>6.1.1</uuid-creator.version> <!-- UUID Creator 库 -->
    <ip2region.version>2.7.0</ip2region.version> <!-- ip2region IP 地址定位库 -->
    <yauaa.version>7.31.0</yauaa.version> <!-- YAUAA User-Agent 解析 -->

    <!-- Web 与模板引擎 -->
    <kaptcha.version>2.3.3</kaptcha.version> <!-- 验证码 -->
    <easycaptcha.version>2.2.5</easycaptcha.version> <!-- EasyCaptcha 验证码 -->
    <velocity.version>2.4.1</velocity.version> <!-- 模板引擎 -->
    <mybatis-plus.version>3.5.12</mybatis-plus.version> <!-- MyBatis Plus -->

    <!-- 安全 -->
    <bcprov-lts8on.version>2.73.6</bcprov-lts8on.version> <!-- BouncyCastle 加密库 -->
    <jjwt.version>0.13.0</jjwt.version> <!-- JSON Web Token (JWT) -->

    <!-- API 与文档 -->
    <springdoc.version>1.8.0</springdoc.version> <!-- SpringDoc OpenAPI -->
    <zalando.version>0.27.0</zalando.version> <!-- Zalando Problem 处理 (支持 Java 8 的最后一个版本) -->

    <!-- 构建工具 -->
    <lombok.version>1.18.38</lombok.version> <!-- Lombok 注解处理器 -->
    <mapstruct.version>1.6.3</mapstruct.version> <!-- MapStruct Bean 映射工具 -->
    <maven-compiler.version>3.11.0</maven-compiler.version> <!-- Maven 编译器插件 (支持 Java 8 的最后一个版本) -->
    <cyclonedx-maven.version>2.9.1</cyclonedx-maven.version> <!-- CycloneDX SBOM 生成插件 -->

    <!-- Maven 插件 -->
    <build-helper.version>3.6.1</build-helper.version> <!-- Build Helper Maven 插件 -->
    <git-build-hook.version>3.5.0</git-build-hook.version> <!-- Git Hook Maven 插件 -->
    <google-java-format.version>1.7</google-java-format.version> <!-- Google Java 格式化插件 (支持 Java 8
    的最后一个版本) -->
    <maven-war.version>3.4.0</maven-war.version> <!-- Maven 插件版本 -->
    <spotless.version>2.30.0</spotless.version> <!-- Spotless 代码格式化插件 (支持 Java 8 的最后一个版本) -->

    <!-- 项目构建设置 -->
    <build.threads>4</build.threads> <!-- 并行构建线程数 -->
  </properties>

  <!-- 聚合模块 -->
  <modules>
    <module>xinjian-admin</module>
    <module>xinjian-common</module>
    <module>xinjian-framework</module>
    <module>xinjian-generator</module>
    <module>xinjian-quartz</module>
    <module>xinjian-starters</module>
    <module>xinjian-modules</module>
  </modules>

  <!-- 依赖管理: 用于统一管理项目所有依赖的版本，子模块无需再指定版本号，保证版本一致性 -->
  <dependencyManagement>
    <dependencies>
      <!-- 内部模块 -->
      <dependency>
        <groupId>com.xinjian</groupId>
        <artifactId>xinjian-common</artifactId>
        <version>${xinjian.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xinjian</groupId>
        <artifactId>xinjian-framework</artifactId>
        <version>${xinjian.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xinjian</groupId>
        <artifactId>xinjian-generator</artifactId>
        <version>${xinjian.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xinjian</groupId>
        <artifactId>xinjian-quartz</artifactId>
        <version>${xinjian.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xinjian</groupId>
        <artifactId>xinjian-starter-redis</artifactId>
        <version>${xinjian.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xinjian</groupId>
        <artifactId>xinjian-starter-mybatis</artifactId>
        <version>${xinjian.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xinjian</groupId>
        <artifactId>xinjian-module-captcha</artifactId>
        <version>${xinjian.version}</version>
      </dependency>

      <!-- Spring Boot 依赖物料清单 (BOM) -->
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- Spring Framework 依赖物料清单 (BOM) -->
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-framework-bom</artifactId>
        <version>${spring-framework.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- MyBatis Plus 依赖物料清单 (BOM) -->
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-bom</artifactId>
        <version>${mybatis-plus.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-generator</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>

      <!-- 数据库和数据源 -->
      <!-- Alibaba Druid 核心库（用于 SQL 解析） -->
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid</artifactId>
        <version>1.2.25</version>
      </dependency>
      <!-- 动态数据源 -->
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        <version>${dynamic-datasource.version}</version>
      </dependency>
      <!-- MySQL 驱动 -->
      <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-j</artifactId>
        <version>${mysql-connector.version}</version>
      </dependency>
      <!-- MariaDB 驱动 -->
      <dependency>
        <groupId>org.mariadb.jdbc</groupId>
        <artifactId>mariadb-java-client</artifactId>
        <version>${mariadb-connector.version}</version>
      </dependency>
      <!-- OceanBase 驱动 -->
      <dependency>
        <groupId>com.oceanbase</groupId>
        <artifactId>oceanbase-client</artifactId>
        <version>${oceanbase-connector.version}</version>
      </dependency>

      <!-- JSON 库 -->
      <!-- Jackson 核心库 -->
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>${jackson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>${jackson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>${jackson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-parameter-names</artifactId>
        <version>${jackson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jdk8</artifactId>
        <version>${jackson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jsr310</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <!-- Problem Spring Web 标准错误处理 RFC 9457 -->
      <dependency>
        <groupId>org.zalando</groupId>
        <artifactId>problem-spring-web-starter</artifactId>
        <version>${zalando.version}</version>
      </dependency>

      <!-- API 与文档 -->
      <!-- SpringDoc OpenAPI UI -->
      <dependency>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi-ui</artifactId>
        <version>${springdoc.version}</version>
      </dependency>
      <!-- SpringDoc OpenAPI Security -->
      <dependency>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi-security</artifactId>
        <version>${springdoc.version}</version>
      </dependency>
      <!-- SpringDoc OpenAPI Data REST -->
      <dependency>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi-data-rest</artifactId>
        <version>${springdoc.version}</version>
      </dependency>

      <!-- 工具库 -->
      <!-- Hutool Java 工具集 -->
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>${hutool.version}</version>
      </dependency>
      <!-- Apache Commons Lang3 -->
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>${commons-lang3.version}</version>
      </dependency>
      <!-- Apache Commons IO -->
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>${commons-io.version}</version>
      </dependency>
      <!-- Oshi 系统信息获取库 -->
      <dependency>
        <groupId>com.github.oshi</groupId>
        <artifactId>oshi-core</artifactId>
        <version>${oshi.version}</version>
      </dependency>
      <!-- Google Guava 核心库 -->
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <!-- Apache POI Excel 操作库 -->
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-ooxml</artifactId>
        <version>${poi.version}</version>
      </dependency>
      <!-- UUID Creator 库 -->
      <dependency>
        <groupId>com.github.f4b6a3</groupId>
        <artifactId>uuid-creator</artifactId>
        <version>${uuid-creator.version}</version>
      </dependency>
      <!-- ip2region IP 地址定位库 -->
      <dependency>
        <groupId>org.lionsoul</groupId>
        <artifactId>ip2region</artifactId>
        <version>${ip2region.version}</version>
      </dependency>
      <!-- YAUAA User-Agent 解析 -->
      <dependency>
        <groupId>nl.basjes.parse.useragent</groupId>
        <artifactId>yauaa</artifactId>
        <version>${yauaa.version}</version>
      </dependency>

      <!-- Web 与模板引擎 -->
      <!-- MyBatis Plus -->
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-jsqlparser-4.9</artifactId>
      </dependency>
      <!-- Velocity 模板引擎 -->
      <dependency>
        <groupId>org.apache.velocity</groupId>
        <artifactId>velocity-engine-core</artifactId>
        <version>${velocity.version}</version>
      </dependency>
      <!-- Kaptcha 验证码生成器 -->
      <dependency>
        <groupId>pro.fessional</groupId>
        <artifactId>kaptcha</artifactId>
        <version>${kaptcha.version}</version>
      </dependency>
      <!-- EasyCaptcha 验证码生成器 -->
      <dependency>
        <groupId>com.pig4cloud.plugin</groupId>
        <artifactId>captcha-spring-boot-starter</artifactId>
        <version>${easycaptcha.version}</version>
      </dependency>

      <!-- 安全 -->
      <!-- JSON Web Token (JWT) -->
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-api</artifactId>
        <version>${jjwt.version}</version>
      </dependency>
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-impl</artifactId>
        <version>${jjwt.version}</version>
        <scope>runtime</scope>
      </dependency>
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-jackson</artifactId>
        <version>${jjwt.version}</version>
        <scope>runtime</scope>
      </dependency>

      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
        <scope>provided</scope>
      </dependency>

      <!-- MapStruct Bean 映射工具 -->
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct-processor</artifactId>
        <version>${mapstruct.version}</version>
        <scope>provided</scope>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <!-- Maven 编译器插件 -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven-compiler.version}</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
          <encoding>${project.build.sourceEncoding}</encoding>
          <compilerArgs>
            <arg>-Xlint:-options</arg>
          </compilerArgs>
          <!-- 启用增量编译 -->
          <useIncrementalCompilation>true</useIncrementalCompilation>
          <!-- 优化编译器内存使用 -->
          <meminitial>256m</meminitial>
          <maxmem>512m</maxmem>
          <annotationProcessorPaths>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
            <path>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>${mapstruct.version}</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
      <!-- CycloneDX 软件物料清单 (SBOM) 生成插件 -->
      <plugin>
        <groupId>org.cyclonedx</groupId>
        <artifactId>cyclonedx-maven-plugin</artifactId>
        <version>${cyclonedx-maven.version}</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>makeAggregateBom</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <!-- Spotless 插件，用于代码格式化 -->
      <plugin>
        <groupId>com.diffplug.spotless</groupId>
        <artifactId>spotless-maven-plugin</artifactId>
        <version>${spotless.version}</version>
        <configuration>
          <java>
            <!-- 应用于所有 Java 文件 -->
            <includes>
              <include>src/main/java/**/*.java</include>
              <include>src/test/java/**/*.java</include>
            </includes>
            <!-- 排序 import -->
            <importOrder>
              <order>static .*,java,javax,org,com</order>
            </importOrder>
            <!-- 应用 Google Java 格式 -->
            <googleJavaFormat>
              <version>${google-java-format.version}</version>
              <style>GOOGLE</style>
            </googleJavaFormat>
            <!-- 使用 Unix 换行符 -->
            <lineEndings>UNIX</lineEndings>
          </java>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>apply</goal>
            </goals>
            <phase>validate</phase>
          </execution>
        </executions>
      </plugin>
      <!-- Git Hook 插件，用于自动化格式化 -->
      <plugin>
        <groupId>com.rudikershaw.gitbuildhook</groupId>
        <artifactId>git-build-hook-maven-plugin</artifactId>
        <version>${git-build-hook.version}</version>
        <configuration>
          <skip>${skipGitHooks}</skip>
          <installHooks>
            <pre-commit>hooks/pre-commit.sh</pre-commit>
          </installHooks>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>install</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
    <!-- 资源文件配置 -->
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>false</filtering>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
        <includes>
          <include>application*</include>
        </includes>
        <filtering>true</filtering>
      </resource>
    </resources>
  </build>

  <!-- Maven 仓库配置 -->
  <repositories>
    <repository>
      <id>public</id>
      <name>aliyun nexus</name>
      <url>https://maven.aliyun.com/repository/public</url>
      <releases>
        <enabled>true</enabled>
      </releases>
    </repository>
  </repositories>

  <!-- Maven 插件仓库配置 -->
  <pluginRepositories>
    <pluginRepository>
      <id>public</id>
      <name>aliyun nexus</name>
      <url>https://maven.aliyun.com/repository/public</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
    </pluginRepository>
  </pluginRepositories>
</project>
