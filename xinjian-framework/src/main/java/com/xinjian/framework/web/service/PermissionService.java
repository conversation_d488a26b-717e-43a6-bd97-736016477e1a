package com.xinjian.framework.web.service;

import com.xinjian.common.core.domain.dto.UserPrincipalDTO;
import com.xinjian.common.service.ISysMenuService;
import com.xinjian.common.service.ISysRoleService;
import com.xinjian.common.service.ISysUserService;
import com.xinjian.common.utils.SecurityUtils;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/** 用户权限服务，封装了获取用户角色和菜单权限的业务逻辑。采用方法重载，为不同调用场景提供最合适的接口。 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionService {

  private final ISysRoleService roleService;
  private final ISysMenuService menuService;
  private final ISysUserService userService;

  /**
   * 根据用户 ID 获取所有权限（角色 + 菜单）
   *
   * @param userId 用户 ID
   * @return 权限集合
   */
  public Set<String> getAllPermissions(Long userId) {
    UserPrincipalDTO principal = userService.selectUserPrincipalById(userId);
    if (principal == null) {
      log.warn("尝试获取一个不存在的用户 (ID: {}) 的所有权限", userId);
      return Collections.emptySet();
    }
    return getAllPermissions(principal);
  }

  /**
   * 根据用户主体获取所有权限（角色 + 菜单）
   *
   * @param principal 用户主体信息
   * @return 权限集合
   */
  public Set<String> getAllPermissions(UserPrincipalDTO principal) {
    Set<String> allPermissions = new HashSet<>();
    allPermissions.addAll(getRolePermission(principal));
    allPermissions.addAll(getMenuPermission(principal));
    return allPermissions;
  }

  /**
   * 根据用户 ID 获取角色权限
   *
   * @param userId 用户 ID
   * @return 角色权限标识集合
   */
  public Set<String> getRolePermission(Long userId) {
    UserPrincipalDTO principal = userService.selectUserPrincipalById(userId);
    if (principal == null) {
      log.warn("尝试获取一个不存在的用户 (ID: {}) 的角色权限", userId);
      return Collections.emptySet();
    }
    return getRolePermission(principal);
  }

  /**
   * 根据用户主体获取角色权限
   *
   * @param principal 用户主体信息
   * @return 角色权限标识集合
   */
  public Set<String> getRolePermission(UserPrincipalDTO principal) {
    Set<String> roles = new HashSet<>();
    if (SecurityUtils.isAdminUser(principal.getUserId())) {
      roles.add("ADMIN"); // 注意：这里返回的是角色键，LoginUser 中会统一加 ROLE_ 前缀
    } else {
      roles.addAll(roleService.selectRoleKeysByUserId(principal.getUserId()));
    }
    return roles;
  }

  /**
   * 根据用户 ID 获取菜单权限
   *
   * @param userId 用户 ID
   * @return 菜单权限标识集合
   */
  public Set<String> getMenuPermission(Long userId) {
    UserPrincipalDTO principal = userService.selectUserPrincipalById(userId);
    if (principal == null) {
      log.warn("尝试获取一个不存在的用户 (ID: {}) 的菜单权限", userId);
      return Collections.emptySet();
    }
    return getMenuPermission(principal);
  }

  /**
   * 根据用户主体获取菜单权限
   *
   * @param principal 用户主体信息
   * @return 菜单权限标识集合
   */
  public Set<String> getMenuPermission(UserPrincipalDTO principal) {
    Set<String> perms = new HashSet<>();
    if (SecurityUtils.isAdminUser(principal.getUserId())) {
      // 管理员拥有所有实际存在的权限
      // perms.add("*:*:*"); // 原有的通配符权限 Spring Security 不支持
      perms.addAll(menuService.selectAllMenuPerms()); // 添加所有实际存在的权限 = 通配符权限
    } else {
      perms.addAll(menuService.selectMenuPermsByUserId(principal.getUserId()));
    }
    return perms;
  }
}
