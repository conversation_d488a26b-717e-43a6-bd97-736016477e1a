package com.xinjian.framework.web.service;

import com.xinjian.common.constant.CacheConstants;
import com.xinjian.common.exception.status429.PasswordAttemptsExceededException;
import com.xinjian.starter.redis.core.RedisClient;
import java.util.concurrent.TimeUnit;
import javax.validation.constraints.NotBlank;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/** 密码服务 - 密码错误次数限制和账户锁定策略 防止暴力破解攻击，使用 Redis 跟踪密码错误次数，超过阈值时暂时锁定账户 */
@Validated
@Component
public class PasswordService {

  private final RedisClient redisClient;
  private final PasswordProperties passwordProperties;

  public PasswordService(
      final RedisClient redisClient, final PasswordProperties passwordProperties) {
    this.redisClient = redisClient;
    this.passwordProperties = passwordProperties;
    validateConfiguration();
  }

  public void validateRetryLimit(@NotBlank(message = "用户名不能为空") final String username) {
    String cacheKey = generatePasswordErrorCacheKey(username);
    Integer retryCount = redisClient.get(cacheKey, Integer.class).orElse(null);

    if (retryCount != null && retryCount >= passwordProperties.getMaxRetryCount()) {
      String errMsg =
          String.format(
              "密码错误次数超过 %d 次，账户已被锁定 %d 分钟",
              passwordProperties.getMaxRetryCount(), passwordProperties.getLockTime());
      throw new PasswordAttemptsExceededException(errMsg);
    }
  }

  public void incrementErrorCountAndCheckLock(
      @NotBlank(message = "用户名不能为空") final String username) {
    String cacheKey = generatePasswordErrorCacheKey(username);
    long retryCount = redisClient.increment(cacheKey, 1);

    if (retryCount == 1) {
      redisClient.expire(cacheKey, passwordProperties.getLockTime(), TimeUnit.MINUTES);
    }

    if (retryCount >= passwordProperties.getMaxRetryCount()) {
      String errMsg =
          String.format(
              "密码错误次数超过 %d 次，账户已被锁定 %d 分钟",
              passwordProperties.getMaxRetryCount(), passwordProperties.getLockTime());
      throw new PasswordAttemptsExceededException(errMsg);
    }
  }

  public void clearPasswordErrorCount(@NotBlank(message = "用户名不能为空") final String username) {
    redisClient.delete(generatePasswordErrorCacheKey(username));
  }

  private String generatePasswordErrorCacheKey(final String username) {
    return CacheConstants.PWD_ERR_CNT_KEY + username;
  }

  @Component
  @ConfigurationProperties(prefix = "user.password")
  public static class PasswordProperties {
    private int maxRetryCount = 5;
    private int lockTime = 10;

    public int getMaxRetryCount() {
      return maxRetryCount;
    }

    public void setMaxRetryCount(final int maxRetryCount) {
      if (maxRetryCount <= 0) {
        throw new IllegalArgumentException("最大重试次数必须大于 0");
      }
      this.maxRetryCount = maxRetryCount;
    }

    public int getLockTime() {
      return lockTime;
    }

    public void setLockTime(final int lockTime) {
      if (lockTime <= 0) {
        throw new IllegalArgumentException("锁定时间必须大于 0");
      }
      this.lockTime = lockTime;
    }
  }

  private void validateConfiguration() {
    if (passwordProperties.getMaxRetryCount() <= 0) {
      throw new IllegalStateException("密码最大重试次数配置无效：" + passwordProperties.getMaxRetryCount());
    }
    if (passwordProperties.getLockTime() <= 0) {
      throw new IllegalStateException("密码锁定时间配置无效：" + passwordProperties.getLockTime());
    }
  }
}
