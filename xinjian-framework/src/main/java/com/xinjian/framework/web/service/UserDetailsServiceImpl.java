package com.xinjian.framework.web.service;

import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.dto.UserPrincipalDTO;
import com.xinjian.common.exception.status403.ForbiddenException;
import com.xinjian.common.service.ISysUserService;
import java.util.Objects;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * Spring Security 用户验证处理服务
 *
 * <p>在用户认证过程中加载用户特定数据，负责查找用户、验证状态，并最终构建一个包含完整权限信息的 UserDetails 对象。
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

  private final ISysUserService userService;
  private final PermissionService permissionService;

  /**
   * 当用户尝试登录时，通过用户名获取用户的详细信息。这是整个认证流程的核心入口。
   *
   * @param username 待认证的用户名
   * @return 一个包含用户核心信息和权限的 {@link UserDetails} 对象
   * @throws UsernameNotFoundException 如果认证过程中出现任何问题（为安全起见，统一抛出此异常）
   */
  @Override
  public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
    try {
      if (!StringUtils.hasText(username)) {
        log.warn("认证失败：尝试使用空的用户名登录");
        throw new UsernameNotFoundException("用户名或密码错误");
      }
      log.debug("开始加载用户详情，用户名：{}", username);

      // 步骤 1: 根据用户名查找用户
      UserPrincipalDTO principal = findUserPrincipalByUsername(username);

      // 步骤 2: 验证用户状态
      validateUserStatus(principal);

      // 步骤 3: 构建并返回包含完整授权信息的 LoginUser 对象
      return buildLoginUser(principal);

    } catch (ForbiddenException | UsernameNotFoundException e) {
      // 已知异常，记录日志并直接抛出
      log.warn("认证失败，用户名：{}, 原因：{}", username, e.getMessage());
      throw e;
    } catch (Exception e) {
      // 未知异常，记录严重错误日志，并统一抛出认证失败异常
      log.error("加载用户详情时发生未知错误，用户名：{}", username, e);
      throw new UsernameNotFoundException("用户名或密码错误");
    }
  }

  /** 查找用户，如果不存在则抛出异常。 */
  private UserPrincipalDTO findUserPrincipalByUsername(String username)
      throws UsernameNotFoundException {
    UserPrincipalDTO principal = userService.selectUserPrincipalByUserName(username);
    if (Objects.isNull(principal)) {
      log.info("认证失败：用户 '{}' 不存在", username);
      throw new UsernameNotFoundException("用户名或密码错误");
    }
    return principal;
  }

  /** 验证用户状态，如果状态异常则抛出异常。 */
  private void validateUserStatus(UserPrincipalDTO principal) throws ForbiddenException {
    if (principal.getIsDeleted()) {
      throw new ForbiddenException("您的账号已被删除");
    }
    if (!principal.getStatus()) {
      throw new ForbiddenException("您的账号已被停用");
    }
  }

  /** 获取权限并构建 LoginUser 对象。 */
  private UserDetails buildLoginUser(UserPrincipalDTO principal) {
    log.debug("开始为用户 '{}' 构建 LoginUser", principal.getUserName());

    // 调用权限服务获取角色和权限
    Set<String> roles = permissionService.getRolePermission(principal);
    Set<String> permissions = permissionService.getMenuPermission(principal);
    log.debug(
        "成功获取用户角色和权限，用户名：{}, 角色数量：{}, 权限数量：{}",
        principal.getUserName(),
        roles.size(),
        permissions.size());

    // 构建 LoginUser，enabled 直接为 true，因为无效状态已在前一步被拦截
    return new LoginUser(
        principal.getUserId(),
        principal.getDeptId(),
        principal.getUserName(),
        principal.getPassword(),
        true,
        permissions,
        roles);
  }
}
