package com.xinjian.framework.web.service;

import com.xinjian.common.core.domain.entity.SysLogininfor;
import com.xinjian.common.service.ISysLogininforService;
import com.xinjian.common.service.location.GeoLocationService;
import com.xinjian.common.utils.ServletUtils;
import com.xinjian.common.utils.ip.IpUtils;
import com.xinjian.common.utils.ua.UserAgentUtils;
import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/** 异步记录登录日志服务 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LoginLogAsyncService {

  private final ISysLogininforService logininforService;
  private final GeoLocationService geoLocationService;

  /**
   * 异步记录登录日志的核心方法
   *
   * @param username 用户名
   * @param status 状态 (e.g., "Success", "Error")
   * @param message 消息
   */
  @Async // 声明这是一个异步方法
  public void recordLogininfor(final String username, final String status, final String message) {
    try {
      // 获取 HTTP 请求相关信息
      // 注意：确保 ServletUtils 可以在异步线程中正确获取 RequestAttributes。
      // Spring Boot 默认配置通常支持，如果不行，可配置 TaskDecorator。
      final String ip = IpUtils.getIpAddr();
      final com.xinjian.common.utils.ua.UserAgentInfo userAgentInfo =
          UserAgentUtils.parse(ServletUtils.getRequest());

      // 如果 IP 地址为空，使用默认值
      if (StringUtils.isEmpty(ip)) {
        log.warn("无法获取 IP 地址，使用默认值 'unknown'");
      }

      // 封装日志对象
      final SysLogininfor logininfor = new SysLogininfor();
      logininfor.setUserName(StringUtils.defaultIfEmpty(username, ""));
      logininfor.setIpaddr(StringUtils.defaultIfEmpty(ip, "unknown"));
      logininfor.setMsg(message);
      logininfor.setLoginTime(LocalDateTime.now());

      // 解析地理位置
      logininfor.setLoginLocation(
          geoLocationService
              .getGeoLocationByIp(StringUtils.defaultIfEmpty(ip, "127.0.0.1"))
              .getSimpleLocation());

      // 解析浏览器和操作系统
      if (userAgentInfo != null) {
        logininfor.setBrowser(userAgentInfo.getFullBrowserInfo());
        logininfor.setOs(userAgentInfo.getFullOsInfo());
      } else {
        logininfor.setBrowser("未知");
        logininfor.setOs("未知");
      }

      // 设置日志状态
      // Success 和 Logout 都表示成功操作
      if ("Success".equals(status) || "Logout".equals(status)) {
        logininfor.setStatus(true);
      } else {
        logininfor.setStatus(false);
      }

      // 插入数据库
      logininforService.insertLogininfor(logininfor);

      log.info("用户 '{}' 登录日志记录成功，状态：{}", username, status);

    } catch (Exception e) {
      log.error("异步记录用户 '{}' 登录日志时发生异常：{}", username, e.getMessage(), e);
    }
  }
}
