package com.xinjian.framework.manager;

import com.xinjian.common.core.domain.entity.SysOperLog;
import com.xinjian.common.utils.Threads;
import com.xinjian.framework.manager.factory.AsyncFactory;
import java.util.TimerTask;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/** 异步任务管理器 */
@Component
@RequiredArgsConstructor
public class AsyncManager {
  private final AsyncFactory asyncFactory;
  private final ScheduledExecutorService executor;

  /** 操作延迟 10 毫秒 */
  private final int OPERATE_DELAY_TIME = 10;

  /**
   * 执行任务
   *
   * @param task 任务
   */
  public void execute(TimerTask task) {
    executor.schedule(task, OPERATE_DELAY_TIME, TimeUnit.MILLISECONDS);
  }

  /**
   * 操作日志记录
   *
   * @param operLog 操作日志信息
   */
  public void recordOper(SysOperLog operLog) {
    execute(asyncFactory.recordOper(operLog));
  }

  /** 停止任务线程池 */
  public void shutdown() {
    Threads.shutdownAndAwaitTermination(executor);
  }
}
