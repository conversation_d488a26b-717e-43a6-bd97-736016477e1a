package com.xinjian.framework.listener;

import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.event.RoleUpdatedEvent;
import com.xinjian.common.service.ISysUserService;
import com.xinjian.framework.web.service.PermissionService;
import com.xinjian.framework.web.service.TokenService;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@Component
@RequiredArgsConstructor
public class UserPermissionCacheListener {

  private final ISysUserService userService;
  private final TokenService tokenService;
  private final PermissionService permissionService;

  /**
   * 监听角色更新事件，并刷新相关用户的权限缓存。使用 @TransactionalEventListener 可以确保此监听器在发布事件的事务成功提交后才执行。
   *
   * @param event 角色更新事件
   */
  @TransactionalEventListener
  @Async // 让缓存刷新操作在独立的线程中异步执行，从而不阻塞当前 API 请求的响应
  public void onRoleUpdated(RoleUpdatedEvent event) {
    Long roleId = event.getRoleId();
    log.info("监听到角色 (ID: {}) 更新事件，开始刷新相关用户的权限缓存。", roleId);

    // 1. 根据角色 ID，查找所有拥有该角色的用户 ID
    List<Long> userIds = userService.findUserIdsByRoleId(roleId);
    if (userIds == null || userIds.isEmpty()) {
      log.info("角色 (ID: {}) 下没有关联的用户，无需刷新缓存。", roleId);
      return;
    }

    // 2. 遍历这些用户，刷新他们的权限缓存
    userIds.forEach(
        userId -> {
          // 检查用户是否在线（即缓存中是否存在该用户的 LoginUser）
          LoginUser cachedLoginUser = tokenService.getLoginUserByUserId(userId);
          if (cachedLoginUser != null) {
            log.debug("正在为在线用户 (ID: {}) 刷新权限缓存...", userId);
            try {
              // 重新获取该用户的完整权限
              Set<String> roles = permissionService.getRolePermission(userId);
              Set<String> permissions = permissionService.getMenuPermission(userId);

              // 创建一个新的 LoginUser 对象
              LoginUser updatedLoginUser =
                  new LoginUser(
                      userId,
                      cachedLoginUser.getDeptId(),
                      cachedLoginUser.getUsername(),
                      cachedLoginUser.getPassword(),
                      cachedLoginUser.isEnabled(),
                      permissions,
                      roles);

              // 使用新的 LoginUser 对象覆盖缓存
              tokenService.setLoginUser(updatedLoginUser);
              log.info("成功刷新了用户 (ID: {}) 的权限缓存。", userId);
            } catch (Exception e) {
              log.error("刷新用户 (ID: {}) 权限缓存时发生错误", userId, e);
            }
          }
        });
  }
}
