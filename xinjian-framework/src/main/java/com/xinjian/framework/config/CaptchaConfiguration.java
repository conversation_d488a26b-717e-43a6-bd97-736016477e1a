package com.xinjian.framework.config;

import com.xinjian.framework.security.captcha.store.RedisCaptchaStore;
import com.xinjian.module.captcha.service.CaptchaStore;
import com.xinjian.starter.redis.core.RedisClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 验证码模块集成配置
 *
 * <p>负责将通用的 captcha 模块与 redis-starter 进行"粘合"。当配置了 xinjian.captcha.store=redis 时，会创建 Redis 验证码存储实现，
 * 覆盖 captcha 模块中默认的内存实现。
 */
@Configuration
public class CaptchaConfiguration {

  /**
   * 创建 Redis 验证码存储实现
   *
   * <p>当应用配置了 xinjian.captcha.store=redis 时，创建一个基于 Redis 的 CaptchaStore Bean，它会覆盖 captcha
   * 模块中默认的内存实现。
   */
  @Bean
  @ConditionalOnProperty(prefix = "xinjian.captcha", name = "store", havingValue = "redis")
  public CaptchaStore redisCaptchaStore(RedisClient redisClient) {
    return new RedisCaptchaStore(redisClient);
  }
}
