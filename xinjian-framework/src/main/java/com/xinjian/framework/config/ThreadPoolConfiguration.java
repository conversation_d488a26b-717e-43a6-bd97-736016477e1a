package com.xinjian.framework.config;

import com.xinjian.common.utils.Threads;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/** 线程池配置 */
@Configuration
public class ThreadPoolConfiguration implements AsyncConfigurer {

  /** 请求上下文装饰器，确保异步任务能访问到请求上下文 */
  private static class RequestContextDecorator implements TaskDecorator {
    @Override
    public Runnable decorate(Runnable runnable) {
      // 获取当前请求上下文
      RequestAttributes context = RequestContextHolder.currentRequestAttributes();
      return () -> {
        try {
          // 在异步线程中设置请求上下文
          RequestContextHolder.setRequestAttributes(context);
          runnable.run();
        } finally {
          // 清理请求上下文
          RequestContextHolder.resetRequestAttributes();
        }
      };
    }
  }
  // 核心线程池大小
  private int corePoolSize = 50;

  // 最大可创建的线程数
  private int maxPoolSize = 200;

  // 队列最大长度
  private int queueCapacity = 1000;

  // 线程池维护线程所允许的空闲时间
  private int keepAliveSeconds = 300;

  @Override
  public ThreadPoolTaskExecutor getAsyncExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setMaxPoolSize(maxPoolSize);
    executor.setCorePoolSize(corePoolSize);
    executor.setQueueCapacity(queueCapacity);
    executor.setKeepAliveSeconds(keepAliveSeconds);
    // 设置任务装饰器，确保异步任务能访问到请求上下文
    executor.setTaskDecorator(new RequestContextDecorator());
    // 线程池对拒绝任务 (无线程可用) 的处理策略
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    executor.initialize();
    return executor;
  }

  /** 执行周期性或定时任务 */
  @Bean(name = "scheduledExecutorService")
  protected ScheduledExecutorService scheduledExecutorService() {
    return new ScheduledThreadPoolExecutor(
        corePoolSize,
        BasicThreadFactory.builder().namingPattern("schedule-pool-%d").daemon(true).build(),
        new ThreadPoolExecutor.CallerRunsPolicy()) {
      @Override
      protected void afterExecute(Runnable r, Throwable t) {
        super.afterExecute(r, t);
        Threads.printException(r, t);
      }
    };
  }
}
