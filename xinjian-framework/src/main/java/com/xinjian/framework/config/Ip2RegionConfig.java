package com.xinjian.framework.config;

import java.io.IOException;
import java.nio.file.Files;
import lombok.extern.slf4j.Slf4j;
import org.lionsoul.ip2region.xdb.Searcher;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;

/** ip2region 配置类 */
@Slf4j
@Configuration
public class Ip2RegionConfig {

  @Value("${app.ip2region.xdb-path:classpath:ipdb/ip2region.xdb}")
  private Resource dbResource;

  /**
   * 创建一个完全基于内存的、线程安全的 Searcher Bean。应用启动时加载，后续请求直接使用内存中的数据，性能最高。
   *
   * @return Searcher 实例，加载失败时返回 null
   */
  @Bean(destroyMethod = "close")
  public Searcher searcher() {
    log.info("--- [Ip2Region] 开始加载 ip2region.xdb 数据 ---");
    try {
      // 读取所有字节
      byte[] dbContent = Files.readAllBytes(dbResource.getFile().toPath());
      log.info("--- [Ip2Region] ip2region.xdb 数据加载完成，文件大小：{} bytes ---", dbContent.length);
      return Searcher.newWithBuffer(dbContent);
    } catch (IOException e) {
      log.error("--- [Ip2Region] ip2region.xdb 数据加载失败，请检查文件是否存在 ---", e);
      return null;
    }
  }
}
