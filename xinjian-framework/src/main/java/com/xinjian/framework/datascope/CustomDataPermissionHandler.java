package com.xinjian.framework.datascope;

import com.baomidou.mybatisplus.extension.plugins.handler.DataPermissionHandler;
import com.xinjian.common.annotation.DataScope;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.dto.SysRoleDTO;
import com.xinjian.common.core.text.Convert;
import com.xinjian.common.service.ISysRoleService;
import com.xinjian.common.utils.SecurityUtils;
import com.xinjian.framework.context.DataScopeContextHolder;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CustomDataPermissionHandler implements DataPermissionHandler {

  private final ObjectProvider<ISysRoleService> roleServiceProvider;

  public CustomDataPermissionHandler(ObjectProvider<ISysRoleService> roleServiceProvider) {
    this.roleServiceProvider = roleServiceProvider;
  }

  // 数据权限常量
  public static final String DATA_SCOPE_ALL = "1";
  public static final String DATA_SCOPE_CUSTOM = "2";
  public static final String DATA_SCOPE_DEPT = "3";
  public static final String DATA_SCOPE_DEPT_AND_CHILD = "4";
  public static final String DATA_SCOPE_SELF = "5";

  @Override
  public Expression getSqlSegment(Expression where, String mappedStatementId) {
    // 1. 获取上下文中的 @DataScope 注解
    DataScope dataScope = DataScopeContextHolder.get();
    // 如果没有注解，则不进行数据权限过滤
    if (dataScope == null) {
      return where;
    }

    // 2. 获取当前登录用户
    LoginUser loginUser = SecurityUtils.getLoginUser();
    if (loginUser == null) {
      return where;
    }

    // 3. 如果是超级管理员，则不过滤数据
    if (SecurityUtils.isAdminUser(loginUser.getUserId())) {
      return where;
    }

    // 4. 构建数据权限 SQL
    String scopeSql = buildDataScopeSql(loginUser, dataScope);
    if (StringUtils.isBlank(scopeSql)) {
      return where;
    }

    try {
      // 5. 将我们生成的权限 SQL 片段，与原始的 where 条件用 AND 连接起来
      Expression scopeExpression = CCJSqlParserUtil.parseCondExpression(scopeSql);
      return where == null ? scopeExpression : new AndExpression(where, scopeExpression);
    } catch (Exception e) {
      log.error("构建数据权限 SQL 失败：{}", e.getMessage(), e);
      return where;
    }
  }

  private String buildDataScopeSql(LoginUser loginUser, DataScope dataScope) {
    String deptAlias = dataScope.deptAlias();
    String userAlias = dataScope.userAlias();
    StringBuilder sqlString = new StringBuilder();
    List<String> conditions = new ArrayList<>();
    List<String> scopeCustomIds = new ArrayList<>();

    // 查询用户的角色
    ISysRoleService roleService = roleServiceProvider.getIfAvailable();
    List<SysRoleDTO> roles = new ArrayList<>();
    if (roleService != null) {
      roles =
          roleService.selectRolePermissionByUserId(loginUser.getUserId()).stream()
              .map(
                  roleKey -> {
                    SysRoleDTO role = new SysRoleDTO();
                    role.setRoleKey(roleKey);
                    return role;
                  })
              .collect(Collectors.toList());
    }

    for (SysRoleDTO role : roles) {
      if (DATA_SCOPE_CUSTOM.equals(role.getDataScope())) {
        scopeCustomIds.add(Convert.toStr(role.getRoleId()));
      }
    }

    for (SysRoleDTO role : roles) {
      String dataScopeType = role.getDataScope();
      if (conditions.contains(dataScopeType)) {
        continue;
      }
      if (DATA_SCOPE_ALL.equals(dataScopeType)) {
        sqlString = new StringBuilder();
        conditions.add(dataScopeType);
        break;
      } else if (DATA_SCOPE_CUSTOM.equals(dataScopeType)) {
        if (scopeCustomIds.size() > 1) {
          // 多个自定数据权限使用 in 查询，避免多次拼接。
          sqlString.append(
              String.format(
                  " OR %s.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id in (%s) ) ",
                  deptAlias, String.join(",", scopeCustomIds)));
        } else {
          sqlString.append(
              String.format(
                  " OR %s.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = %s ) ",
                  deptAlias, role.getRoleId()));
        }
      } else if (DATA_SCOPE_DEPT.equals(dataScopeType)) {
        sqlString.append(String.format(" OR %s.dept_id = %s ", deptAlias, loginUser.getDeptId()));
      } else if (DATA_SCOPE_DEPT_AND_CHILD.equals(dataScopeType)) {
        sqlString.append(
            String.format(
                " OR %s.dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = %s or find_in_set( %s , ancestors ) )",
                deptAlias, loginUser.getDeptId(), loginUser.getDeptId()));
      } else if (DATA_SCOPE_SELF.equals(dataScopeType)) {
        if (StringUtils.isNotBlank(userAlias)) {
          sqlString.append(String.format(" OR %s.user_id = %s ", userAlias, loginUser.getUserId()));
        } else {
          // 数据权限为仅本人且没有 userAlias 别名不查询任何数据
          sqlString.append(String.format(" OR %s.dept_id = 0 ", deptAlias));
        }
      }
      conditions.add(dataScopeType);
    }

    // 角色都不包含传递过来的权限字符，这个时候 sqlString 也会为空，所以要限制一下，不查询任何数据
    if (conditions == null || conditions.isEmpty()) {
      sqlString.append(String.format(" OR %s.dept_id = 0 ", deptAlias));
    }

    String resultSql = sqlString.toString();
    if (StringUtils.isNotBlank(resultSql)) {
      // 去掉开头的 " OR "，并用括号括起来
      return "(" + resultSql.substring(4) + ")";
    }

    return "";
  }
}
