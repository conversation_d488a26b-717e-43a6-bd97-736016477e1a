<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>xinjian</artifactId>
    <groupId>com.xinjian</groupId>
    <version>1.0.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>xinjian-framework</artifactId>

  <description>框架核心模块</description>

  <dependencies>

    <!-- 内部模块依赖 -->
    <dependency>
      <groupId>com.xinjian</groupId>
      <artifactId>xinjian-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xinjian</groupId>
      <artifactId>xinjian-starter-redis</artifactId>
      <version>${xinjian.version}</version>
    </dependency>
    <dependency>
      <groupId>com.xinjian</groupId>
      <artifactId>xinjian-module-captcha</artifactId>
      <version>${xinjian.version}</version>
    </dependency>
    <!-- Spring Boot Web, 提供 Web 应用开发所需的核心功能 -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <!-- Spring Boot AOP, 提供面向切面编程的支持 -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-aop</artifactId>
    </dependency>

    
    <!-- Oshi, 用于获取操作系统和硬件信息的库 -->
    <dependency>
      <groupId>com.github.oshi</groupId>
      <artifactId>oshi-core</artifactId>
    </dependency>

    <!-- Problem Spring Web 标准错误处理 RFC 9457 -->
    <dependency>
      <groupId>org.zalando</groupId>
      <artifactId>problem-spring-web-starter</artifactId>
    </dependency>

    <!-- Lombok，通过注解简化 Java 代码 -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>

</project>
